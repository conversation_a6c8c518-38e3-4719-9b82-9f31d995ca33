<resources>
    <style name="Widget.Theme.EftaaPay.ButtonBar.Fullscreen" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="android:background">@color/black_overlay</item>
        <item name="android:buttonBarStyle">?android:attr/buttonBarStyle</item>
        <item name="android:fontFamily">@font/helvetica</item>

    </style>
    <style name="TransparentDialogTheme" parent="Theme.AppCompat.Light.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowMinWidthMajor">90%</item> <!-- Optional: Adjust dialog width -->
        <item name="android:windowMinWidthMinor">90%</item> <!-- Optional: Adjust dialog width -->
        <item name="android:fontFamily">@font/helvetica</item>
    </style>

    <!-- Custom style for the cancel button with red outline -->
    <style name="RedOutlineButton" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="strokeColor">@color/red</item>
        <item name="strokeWidth">2dp</item>
        <item name="android:textColor">@android:color/black</item>
        <item name="backgroundTint">@android:color/transparent</item>
        <item name="rippleColor">@color/red</item>
        <item name="cornerRadius">12dp</item>
    </style>

    <!-- Custom style for the create button with green outline -->
    <style name="GreenOutlineButton" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="strokeColor">@color/lavender</item>
        <item name="strokeWidth">2dp</item>
        <item name="android:textColor">@color/lavender</item>
        <item name="backgroundTint">@android:color/transparent</item>
        <item name="rippleColor">@color/lavender</item>
        <item name="cornerRadius">12dp</item>
    </style>
</resources>