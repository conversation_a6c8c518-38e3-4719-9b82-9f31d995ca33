<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/create_static_qr_overlay"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:visibility="gone"
    android:background="@color/platinum"
    android:clickable="true"
    android:focusable="true">

    <ImageButton
        android:id="@+id/back_button_dialog"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="24dp"
        android:background="@drawable/modern_back_button_bg"
        android:contentDescription="@string/cancel"
        android:elevation="4dp"
        android:padding="8dp"
        android:scaleType="centerInside"
        android:src="@android:drawable/ic_menu_close_clear_cancel"
        app:tint="@android:color/white" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="70dp"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp"
            android:background="@color/platinum">

            <!-- Header -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/create_static_qr_code"
                android:textSize="28sp"
                android:textStyle="bold"
                android:textColor="@color/black"
                android:gravity="center"
                android:layout_marginBottom="30dp" />

            <!-- Name Field -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/qr_code_name"
                android:textStyle="bold"
                android:textSize="16sp"
                android:layout_marginBottom="8dp" />

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="3dp"
                app:cardBackgroundColor="@android:color/white">

                <EditText
                    android:id="@+id/static_qr_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Enter QR code name"
                    android:inputType="text"
                    android:textSize="16sp"
                    android:background="@android:color/transparent"
                    android:padding="16dp" />
            </androidx.cardview.widget.CardView>

            <!-- Amount Field -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Amount"
                android:textStyle="bold"
                android:textSize="16sp"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="8dp" />

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="3dp"
                app:cardBackgroundColor="@android:color/white">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <TextView
                        android:id="@+id/static_qr_currency_symbol"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@android:color/black"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <EditText
                        android:id="@+id/static_qr_amount"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="16dp"
                        android:hint="@string/enter_amount"
                        android:inputType="numberDecimal"
                        android:textSize="16sp"
                        android:background="@android:color/transparent"
                        android:padding="0dp" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="40dp"
                android:gravity="center">

                <Button
                    android:id="@+id/cancel_static_qr_button"
                    android:layout_width="120dp"
                    android:layout_height="55dp"
                    android:text="@string/cancel"
                    android:textColor="@color/white"
                    android:background="@drawable/button_solid_red"
                    android:layout_marginEnd="8dp"
                    android:padding="12dp" />

                <Button
                    android:id="@+id/create_static_qr_button"
                    android:layout_width="0dp"
                    android:layout_height="55dp"
                    android:layout_weight="1"
                    android:text="@string/create"
                    android:textColor="@color/white"
                    android:background="@drawable/button_solid_green"
                    android:layout_marginStart="8dp"
                    android:padding="14dp" />
            </LinearLayout>
        </LinearLayout>
    </ScrollView>
</FrameLayout>
