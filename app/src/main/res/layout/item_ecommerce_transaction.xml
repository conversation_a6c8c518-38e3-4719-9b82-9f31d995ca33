<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="8dp"
    app:cardBackgroundColor="?attr/colorSurface">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="10dp">

        <ImageView
            android:id="@+id/transaction_icon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/transaction_arrows"
            android:layout_marginEnd="12dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Left side: Transaction ID and Date -->
        <LinearLayout
            android:id="@+id/left_container"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/right_container"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            app:layout_constraintStart_toEndOf="@+id/transaction_icon"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/transaction_id"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Transaction Title"
                android:textSize="16sp"
                android:textStyle="bold" />

<!--            <TextView-->
<!--                android:id="@+id/transaction_description"-->
<!--                android:layout_width="wrap_content"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:text="Transaction Description"-->
<!--                android:textColor="@color/gray"-->
<!--                android:textSize="10sp" />-->

            <TextView
                android:id="@+id/transaction_date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="4dp"
                android:text="Transaction Time"
                android:textColor="@color/gray"
                android:textSize="10sp" />
        </LinearLayout>

        <!-- Right side: Amount and Status -->
        <LinearLayout
            android:id="@+id/right_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="end"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/left_container">

            <TextView
                android:id="@+id/transaction_amount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/lavender"
                android:textSize="16sp"
                android:textStyle="bold"
                android:layout_marginBottom="4dp" />

            <TextView
                android:id="@+id/transaction_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                android:textStyle="bold"
                android:gravity="center"
                android:paddingStart="8dp"
                android:paddingEnd="8dp"
                android:paddingTop="2dp"
                android:paddingBottom="2dp"
                android:textColor="@android:color/white" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>