<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginStart="8dp"
    app:cardCornerRadius="20dp"
    app:cardElevation="2dp"
    android:backgroundTint="#EEEEEE">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="2dp">

        <TextView
            android:id="@+id/dynamic_tab"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/selected_tab_background"
            android:paddingStart="12dp"
            android:paddingTop="6dp"
            android:paddingEnd="12dp"
            android:paddingBottom="6dp"
            android:text="Dynamic"
            android:textColor="@android:color/white"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/static_tab"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@android:color/transparent"
            android:paddingStart="12dp"
            android:paddingTop="6dp"
            android:paddingEnd="12dp"
            android:paddingBottom="6dp"
            android:text="Static"
            android:textColor="@android:color/black"
            android:textSize="14sp" />
    </LinearLayout>
</androidx.cardview.widget.CardView>
