<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/platinum"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_left1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.04" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_right1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.96" />

<ScrollView
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">
<androidx.constraintlayout.widget.ConstraintLayout
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_left"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.04" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_right"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.96" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_top"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.03" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_bottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="1.0" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_horizontal_bottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_begin="875dp" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_horizontal_transactions"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.91" />

    <TextView
        android:id="@+id/settings_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="@string/settings_title"
        android:textColor="@android:color/black"
        android:textSize="38sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@+id/guideline_bottom"
        app:layout_constraintEnd_toStartOf="@+id/guideline_right"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="@+id/guideline_left"
        app:layout_constraintTop_toTopOf="@+id/guideline_top"
        app:layout_constraintVertical_bias="0.0" />

    <TextView
        android:id="@+id/personal_info_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="30dp"
        android:text="@string/personal_info_title"
        android:textColor="@color/lavender"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@+id/guideline_right"
        app:layout_constraintStart_toStartOf="@+id/guideline_left"
        app:layout_constraintTop_toBottomOf="@id/settings_title" />


    <androidx.cardview.widget.CardView
        android:id="@+id/name_card"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:layout_marginTop="6dp"
        app:cardBackgroundColor="?attr/colorSurface"
        app:cardCornerRadius="12dp"
        app:layout_constraintEnd_toEndOf="@id/guideline_right"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="@id/guideline_left"
        app:layout_constraintTop_toBottomOf="@id/personal_info_title">

        <LinearLayout
            android:id="@+id/name_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="12dp"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/name_label"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/name_label"
                android:textColor="?attr/colorOnSurface"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/name_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="?attr/colorOnSurface"
                android:textSize="14sp" />

            <EditText
                android:id="@+id/name_edit"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:hint="@string/enter_name"
                android:textColor="?attr/colorOnSurface"
                android:textSize="14sp"
                android:visibility="gone"
                android:background="@null" />

            <ImageButton
                android:id="@+id/edit_name_button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_edit"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:layout_marginStart="8dp" />
        </LinearLayout>
    </androidx.cardview.widget.CardView>


    <androidx.cardview.widget.CardView
        android:id="@+id/email_card"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:layout_marginTop="6dp"
        app:cardBackgroundColor="?attr/colorSurface"
        app:cardCornerRadius="12dp"
        app:layout_constraintEnd_toEndOf="@id/guideline_right"
        app:layout_constraintStart_toStartOf="@id/guideline_left"
        app:layout_constraintTop_toBottomOf="@id/name_card">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="12dp">

            <TextView
                android:id="@+id/email_label"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/email_label"
                android:textColor="?attr/colorOnSurface"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/email_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="?attr/colorOnSurface"
                android:textSize="14sp" />
        </LinearLayout>
    </androidx.cardview.widget.CardView>


    <androidx.cardview.widget.CardView
        android:id="@+id/address_card"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:layout_marginTop="6dp"
        app:cardBackgroundColor="?attr/colorSurface"
        app:cardCornerRadius="12dp"
        app:layout_constraintEnd_toEndOf="@id/guideline_right"
        app:layout_constraintStart_toStartOf="@id/guideline_left"
        app:layout_constraintTop_toBottomOf="@id/email_card">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="12dp">

            <TextView
                android:id="@+id/address_label"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/address_label"
                android:textColor="?attr/colorOnSurface"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/address_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="?attr/colorOnSurface"
                android:textSize="14sp" />
        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/terminal_info_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/terminal_info_title"
        android:textColor="@color/lavender"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@+id/guideline_right"
        app:layout_constraintStart_toStartOf="@+id/guideline_left"
        app:layout_constraintTop_toBottomOf="@id/address_card" />

    <!-- Terminal Name Card -->
    <androidx.cardview.widget.CardView
        android:id="@+id/terminal_name"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:layout_marginTop="6dp"
        app:cardBackgroundColor="?attr/colorSurface"
        app:cardCornerRadius="12dp"
        app:layout_constraintEnd_toEndOf="@id/guideline_right"
        app:layout_constraintStart_toStartOf="@id/guideline_left"
        app:layout_constraintTop_toBottomOf="@id/terminal_info_title">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="12dp"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/terminal_name_label"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/terminal_label"
                android:textColor="?attr/colorOnSurface"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/terminal_name_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="?attr/colorOnSurface"
                android:textSize="14sp" />

            <ImageView
                android:id="@+id/terminal_name_copy"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginStart="8dp"
                android:src="@drawable/copy_icon"
                android:contentDescription="@string/copy_icon"
                android:clickable="true"
                android:focusable="true" />
        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <!-- Terminal ID Card -->
    <androidx.cardview.widget.CardView
        android:id="@+id/terminal_id"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:layout_marginTop="6dp"
        app:cardBackgroundColor="?attr/colorSurface"
        app:cardCornerRadius="12dp"
        app:layout_constraintEnd_toEndOf="@id/guideline_right"
        app:layout_constraintStart_toStartOf="@id/guideline_left"
        app:layout_constraintTop_toBottomOf="@id/terminal_name">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="12dp"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/terminal_id_label"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/terminal_id_label"
                android:textColor="?attr/colorOnSurface"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/terminal_id_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="?attr/colorOnSurface"
                android:textSize="14sp" />

            <ImageView
                android:id="@+id/terminal_id_copy"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginStart="8dp"
                android:src="@drawable/copy_icon"
                android:contentDescription="@string/copy_icon"
                android:clickable="true"
                android:focusable="true" />
        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <!-- Device ID Card -->
    <androidx.cardview.widget.CardView
        android:id="@+id/device_id"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:layout_marginTop="6dp"
        app:cardBackgroundColor="?attr/colorSurface"
        app:cardCornerRadius="12dp"
        app:layout_constraintEnd_toEndOf="@id/guideline_right"
        app:layout_constraintStart_toStartOf="@id/guideline_left"
        app:layout_constraintTop_toBottomOf="@id/terminal_id">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="12dp"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/device_id_label"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/device_label"
                android:textColor="?attr/colorOnSurface"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/device_id_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="?attr/colorOnSurface"
                android:textSize="14sp" />

            <ImageView
                android:id="@+id/device_id_copy"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginStart="8dp"
                android:src="@drawable/copy_icon"
                android:contentDescription="@string/copy_icon"
                android:clickable="true"
                android:focusable="true" />
        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/VAT_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/vat"
        android:textColor="@color/lavender"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@+id/guideline_right"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="@+id/guideline_left"
        app:layout_constraintTop_toBottomOf="@id/device_id" />

    <androidx.cardview.widget.CardView
        android:id="@+id/vat_card"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:layout_marginTop="6dp"
        android:layout_marginBottom="10dp"

        app:cardBackgroundColor="?attr/colorSurface"
        app:cardCornerRadius="12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/guideline_right"
        app:layout_constraintStart_toStartOf="@id/guideline_left"
        app:layout_constraintTop_toBottomOf="@id/VAT_title"
        app:layout_constraintVertical_bias="0.0">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="12dp">

            <TextView
                android:id="@+id/vat_label"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/choose_vat"
                android:textColor="?attr/colorOnSurface"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/vat_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="?attr/colorOnSurface"
                android:textSize="14sp" />
        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/tip_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/terminal_settings"
        android:textColor="@color/lavender"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@+id/guideline_right"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="@+id/guideline_left"
        app:layout_constraintTop_toBottomOf="@id/vat_card" />

    <androidx.cardview.widget.CardView
        android:id="@+id/tip_card"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:layout_marginTop="6dp"
        android:layout_marginBottom="10dp"

        app:cardBackgroundColor="?attr/colorSurface"
        app:cardCornerRadius="12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/guideline_right"
        app:layout_constraintStart_toStartOf="@id/guideline_left"
        app:layout_constraintTop_toBottomOf="@id/tip_title"
        app:layout_constraintVertical_bias="0.0">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="12dp">

            <TextView
                android:id="@+id/tip_label"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/activate_tip_feature"
                android:textColor="?attr/colorOnSurface"
                android:textSize="14sp" />

            <Switch
                android:id="@+id/tip_switch"
                android:layout_width="50dp"
                android:layout_height="30dp" />
        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/security_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/security_title"
        android:textColor="@color/lavender"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@+id/guideline_right"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="@+id/guideline_left"
        app:layout_constraintTop_toBottomOf="@id/tip_card" />

    <androidx.cardview.widget.CardView
        android:id="@+id/biometric_switch_card"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:layout_marginTop="6dp"
        app:cardBackgroundColor="?attr/colorSurface"
        app:cardCornerRadius="12dp"
        app:layout_constraintEnd_toEndOf="@id/guideline_right"
        app:layout_constraintStart_toStartOf="@id/guideline_left"
        app:layout_constraintTop_toBottomOf="@id/security_title">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="12dp">

            <TextView
                android:id="@+id/biometric_label"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/biometric"
                android:textColor="?attr/colorOnSurface"
                android:textSize="14sp" />

            <Switch
                android:id="@+id/biometric_switch"
                android:layout_width="50dp"
                android:layout_height="30dp" />
        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <androidx.cardview.widget.CardView
        android:id="@+id/problem_card"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:layout_marginTop="6dp"
        android:layout_marginBottom="10dp"

        app:cardBackgroundColor="?attr/colorSurface"
        app:cardCornerRadius="12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/guideline_right"
        app:layout_constraintStart_toStartOf="@id/guideline_left"
        app:layout_constraintTop_toBottomOf="@id/biometric_switch_card"
        app:layout_constraintVertical_bias="0.0">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="12dp">

            <TextView
                android:id="@+id/problem_label"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/encounterissue"
                android:textColor="?attr/colorOnSurface"
                android:textSize="14sp" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/arrow_right" />
        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <androidx.cardview.widget.CardView
        android:id="@+id/termsofuse_card"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:layout_marginTop="6dp"
        android:layout_marginBottom="10dp"

        app:cardBackgroundColor="?attr/colorSurface"
        app:cardCornerRadius="12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/guideline_right"
        app:layout_constraintStart_toStartOf="@id/guideline_left"
        app:layout_constraintTop_toBottomOf="@id/problem_card"
        app:layout_constraintVertical_bias="0.0">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="12dp">

            <TextView
                android:id="@+id/termsofuse_label"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/terms_of_use"
                android:textColor="?attr/colorOnSurface"
                android:textSize="14sp" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/arrow_right" />
        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <androidx.cardview.widget.CardView
        android:id="@+id/privacypolicy_card"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:layout_marginTop="6dp"
        android:layout_marginBottom="10dp"

        app:cardBackgroundColor="?attr/colorSurface"
        app:cardCornerRadius="12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/guideline_right"
        app:layout_constraintStart_toStartOf="@id/guideline_left"
        app:layout_constraintTop_toBottomOf="@id/termsofuse_card"
        app:layout_constraintVertical_bias="0.0">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="12dp">

            <TextView
                android:id="@+id/privacy_label"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/privacy_policy"
                android:textColor="?attr/colorOnSurface"
                android:textSize="14sp" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/arrow_right" />
        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <androidx.cardview.widget.CardView
        android:id="@+id/logout_card"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:layout_marginTop="6dp"
        android:layout_marginBottom="79dp"
        app:cardBackgroundColor="?attr/colorSurface"
        app:cardCornerRadius="12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/guideline_right"
        app:layout_constraintStart_toStartOf="@id/guideline_left"
        app:layout_constraintTop_toBottomOf="@id/privacypolicy_card">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="12dp">

            <TextView
                android:id="@+id/logout_label"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/logout"
                android:textColor="?attr/colorOnSurface"
                android:textSize="14sp" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/logout_icon" />
        </LinearLayout>
    </androidx.cardview.widget.CardView>


</androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>
    <androidx.cardview.widget.CardView
        android:id="@+id/navbarcardView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="30sp"
        android:layout_marginBottom="15sp"
        android:elevation="8dp"
        app:cardBackgroundColor="@color/card_background"
        app:cardCornerRadius="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/guideline_right1"
        app:layout_constraintStart_toStartOf="@id/guideline_left1">

        <com.google.android.material.bottomnavigation.BottomNavigationView
            android:id="@+id/bottom_navigation"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:backgroundTint="@color/navbar_background"
            app:itemIconSize="30sp"
            app:itemIconTint="@color/navbar_icon_color"
            app:itemTextColor="@color/navbar_text_color"
            app:labelVisibilityMode="labeled"
            app:menu="@menu/bottom_nav_menu" />
    </androidx.cardview.widget.CardView>
</androidx.constraintlayout.widget.ConstraintLayout>
