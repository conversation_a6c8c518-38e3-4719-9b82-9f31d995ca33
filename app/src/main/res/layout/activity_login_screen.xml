<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layout_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white">

    <ImageView
        android:id="@+id/image_bottom_shape"
        android:layout_width="295sp"
        android:layout_height="535sp"
        android:scaleType="fitXY"
        android:src="@drawable/bg_bottom_shape"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_percent="0.6"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="1.0"
        app:layout_constraintWidth_percent="0.6" />

    <LinearLayout
        android:id="@+id/content_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="128dp"
        android:gravity="top"
        android:orientation="vertical"
        android:paddingHorizontal="32dp"

        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.0"
        app:layout_constraintWidth_max="600dp">

        <!-- Title: "Welcome Back!" -->
        <TextView
            android:id="@+id/text_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/welcome_back"
            android:textColor="@android:color/black"
            android:textSize="32sp"
            android:textStyle="bold" />

        <!-- Subtitle -->
        <TextView
            android:id="@+id/text_subtitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="@string/enter_your_username_password"
            android:textColor="@android:color/darker_gray"
            android:textSize="16sp" />

        <!-- Email field with bottom line -->
        <LinearLayout
            android:id="@+id/layout_email"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_email_label"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/email_address"
                android:textColor="@android:color/black"
                android:textSize="14sp" />

            <EditText
                android:id="@+id/et_email"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@android:color/transparent"
                android:inputType="textEmailAddress"
                android:paddingTop="4dp"
                android:paddingBottom="4dp"
                android:textColor="@android:color/black"
                android:textSize="14sp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@android:color/darker_gray" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/layout_password"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_password_label"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/password"
                android:textColor="@android:color/black"
                android:textSize="14sp" />

            <!-- Container for password field and toggle icon -->
            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <EditText
                    android:id="@+id/et_password"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@android:color/transparent"
                    android:inputType="textPassword"
                    android:paddingTop="4dp"
                    android:paddingBottom="4dp"
                    android:paddingEnd="40dp"
                    android:textColor="@android:color/black"
                    android:textSize="14sp" />

                <ImageView
                    android:id="@+id/iv_password_toggle"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_gravity="end|center_vertical"
                    android:layout_marginEnd="8dp"
                    android:src="@drawable/ic_visibility_off"
                    android:contentDescription="@string/toggle_password_visibility"
                    android:clickable="true"
                    android:focusable="true" />
            </FrameLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@android:color/darker_gray" />
        </LinearLayout>


        <LinearLayout
            android:id="@+id/ll_continue_and_fingerprint_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="40sp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/ll_login_btn"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="30dp"
                android:layout_weight="1"
                android:background="@drawable/button_outline_green_full_rounded"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="horizontal"
                android:padding="16dp">

                <TextView
                    android:id="@+id/tv_login_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/login"
                    android:textAllCaps="false"
                    android:textColor="@android:color/black"
                    android:textSize="18sp"
                    android:textStyle="bold" />
            </LinearLayout>

            <!-- Fingerprint icon -->
            <ImageView
                android:id="@+id/image_fingerprint"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginEnd="25dp"
                android:contentDescription="@string/fingerprint_icon"
                android:src="@drawable/ic_fingerprint" />
        </LinearLayout>

        <TextView
            android:id="@+id/text_merchant_link"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="100sp"
            android:clickable="true"
            android:focusable="true"
            android:textAlignment="center"
            android:text="@string/partner_with_us_as_a_merchant"
            android:textColor="@android:color/black"
            android:textSize="20sp" />
    </LinearLayout>

    <include layout="@layout/loader_overlay" />
    <include layout="@layout/dialog_permission_required" />
</androidx.constraintlayout.widget.ConstraintLayout>
