<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/platinum">

    <!-- Guidelines -->
    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_vertical_left"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.05" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_vertical_right"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.95" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_horizontal_top"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.04" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_horizontal_bottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.98" />

    <!-- Title -->
    <TextView
        android:id="@+id/confirmation_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="25dp"
        android:text="@string/confirm_payment"
        android:textColor="@color/black"
        android:textSize="24sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="@id/guideline_vertical_right"
        app:layout_constraintStart_toStartOf="@id/guideline_vertical_left"
        app:layout_constraintTop_toTopOf="@id/guideline_horizontal_top" />

    <!-- Total Amount Label -->
    <TextView
        android:id="@+id/total_amount_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:text="@string/total_amount"
        android:textSize="18sp"
        android:textColor="@color/lite_gray"
        app:layout_constraintEnd_toEndOf="@id/guideline_vertical_right"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="@id/guideline_vertical_left"
        app:layout_constraintTop_toBottomOf="@id/confirmation_title" />

    <!-- Total Amount Display -->
    <TextView
        android:id="@+id/total_amount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:textSize="32sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        app:layout_constraintEnd_toEndOf="@id/guideline_vertical_right"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="@id/guideline_vertical_left"
        app:layout_constraintTop_toBottomOf="@id/total_amount_label" />

    <!-- Tip Percentage Display -->
    <TextView
        android:id="@+id/tip_amount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/fivepercent"
        android:textSize="16sp"
        android:textColor="@color/lite_gray"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/guideline_vertical_right"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="@id/guideline_vertical_left"
        app:layout_constraintTop_toBottomOf="@id/total_amount" />

    <!-- Tip Options Section Title -->
    <TextView
        android:id="@+id/select_tip_text"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:text="@string/add_a_tip"
        android:textSize="16sp"
        android:textColor="@color/black"
        app:layout_constraintEnd_toEndOf="@id/guideline_vertical_right"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="@id/guideline_vertical_left"
        app:layout_constraintTop_toBottomOf="@id/tip_amount" />

    <!-- Tip Options Container -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/tip_options_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        app:layout_constraintEnd_toEndOf="@id/guideline_vertical_right"
        app:layout_constraintStart_toStartOf="@id/guideline_vertical_left"
        app:layout_constraintTop_toBottomOf="@id/select_tip_text">

        <!-- First Row: 5% and 10% -->
        <ImageButton
            android:id="@+id/tip_5"
            android:layout_width="0dp"
            android:layout_height="59dp"
            android:layout_marginEnd="8dp"
            android:background="@drawable/button_outline_green"
            android:padding="12dp"
            app:layout_constraintWidth_percent="0.48"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tip_5_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/fivepercent"
            android:textColor="@color/black"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@id/tip_5"
            app:layout_constraintEnd_toEndOf="@id/tip_5"
            app:layout_constraintStart_toStartOf="@id/tip_5"
            app:layout_constraintTop_toTopOf="@id/tip_5" />

        <ImageButton
            android:id="@+id/tip_10"
            android:layout_width="0dp"
            android:layout_height="59dp"
            android:layout_marginStart="8dp"
            android:background="@drawable/button_outline_green"
            android:padding="12dp"
            app:layout_constraintWidth_percent="0.48"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tip_10_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/tenpercent"
            android:textColor="@color/black"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@id/tip_10"
            app:layout_constraintEnd_toEndOf="@id/tip_10"
            app:layout_constraintStart_toStartOf="@id/tip_10"
            app:layout_constraintTop_toTopOf="@id/tip_10" />

        <!-- Second Row: 15%, 20%, and No Thanks -->
        <ImageButton
            android:id="@+id/tip_15"
            android:layout_width="0dp"
            android:layout_height="59dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="8dp"
            android:background="@drawable/button_outline_green"
            android:padding="12dp"
            app:layout_constraintWidth_percent="0.31"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tip_5" />

        <TextView
            android:id="@+id/tip_15_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/fifteenpercent"
            android:textColor="@color/black"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@id/tip_15"
            app:layout_constraintEnd_toEndOf="@id/tip_15"
            app:layout_constraintStart_toStartOf="@id/tip_15"
            app:layout_constraintTop_toTopOf="@id/tip_15" />

        <ImageButton
            android:id="@+id/tip_20"
            android:layout_width="0dp"
            android:layout_height="59dp"
            android:layout_marginTop="16dp"
            android:layout_marginStart="4dp"
            android:layout_marginEnd="4dp"
            android:background="@drawable/button_outline_green"
            android:padding="12dp"
            app:layout_constraintWidth_percent="0.31"
            app:layout_constraintStart_toEndOf="@id/tip_15"
            app:layout_constraintEnd_toStartOf="@id/tip_none"
            app:layout_constraintTop_toBottomOf="@id/tip_5" />

        <TextView
            android:id="@+id/tip_20_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/twentypercent"
            android:textColor="@color/black"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@id/tip_20"
            app:layout_constraintEnd_toEndOf="@id/tip_20"
            app:layout_constraintStart_toStartOf="@id/tip_20"
            app:layout_constraintTop_toTopOf="@id/tip_20" />

        <ImageButton
            android:id="@+id/tip_none"
            android:layout_width="0dp"
            android:layout_height="59dp"
            android:layout_marginTop="16dp"
            android:layout_marginStart="8dp"
            android:background="@drawable/button_outline_green"
            android:padding="12dp"
            app:layout_constraintWidth_percent="0.31"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tip_10" />

        <TextView
            android:id="@+id/tip_none_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/zeropercent"
            android:textColor="@color/black"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@id/tip_none"
            app:layout_constraintEnd_toEndOf="@id/tip_none"
            app:layout_constraintStart_toStartOf="@id/tip_none"
            app:layout_constraintTop_toTopOf="@id/tip_none" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Divider -->
    <View
        android:id="@+id/divider"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginTop="16dp"
        android:background="@color/gray"
        app:layout_constraintTop_toBottomOf="@id/tip_options_container"
        app:layout_constraintStart_toStartOf="@id/guideline_vertical_left"
        app:layout_constraintEnd_toEndOf="@id/guideline_vertical_right" />

    <!-- Confirm Button -->
    <ImageButton
        android:id="@+id/confirm_button"
        android:layout_width="0dp"
        android:layout_height="59dp"
        android:layout_marginTop="32dp"
        android:background="@drawable/selected_button"
        android:padding="12dp"
        app:layout_constraintWidth_percent="0.8"
        app:layout_constraintStart_toStartOf="@id/guideline_vertical_left"
        app:layout_constraintEnd_toEndOf="@id/guideline_vertical_right"
        app:layout_constraintTop_toBottomOf="@id/divider" />

    <TextView
        android:id="@+id/confirm_button_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/confirm_payment"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/confirm_button"
        app:layout_constraintEnd_toEndOf="@id/confirm_button"
        app:layout_constraintStart_toStartOf="@id/confirm_button"
        app:layout_constraintTop_toTopOf="@id/confirm_button" />

    <!-- Cancel Button -->
    <ImageButton
        android:id="@+id/cancel_button"
        android:layout_width="0dp"
        android:layout_height="59dp"
        android:layout_marginTop="16dp"
        android:background="@drawable/button_outline_green"
        android:padding="12dp"
        app:layout_constraintEnd_toEndOf="@id/guideline_vertical_right"
        app:layout_constraintStart_toStartOf="@id/guideline_vertical_left"
        app:layout_constraintTop_toBottomOf="@id/confirm_button"
        app:layout_constraintVertical_bias="0.0"
        app:layout_constraintWidth_percent="0.8" />

    <TextView
        android:id="@+id/cancel_button_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/cancel"
        android:textColor="@color/lite_gray"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/cancel_button"
        app:layout_constraintEnd_toEndOf="@id/cancel_button"
        app:layout_constraintStart_toStartOf="@id/cancel_button"
        app:layout_constraintTop_toTopOf="@id/cancel_button" />

    <!-- Bottom Navigation -->
    <androidx.cardview.widget.CardView
        android:id="@+id/navbarcardView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:elevation="8dp"
        app:cardCornerRadius="10dp"
        app:cardBackgroundColor="@color/card_background"
        app:layout_constraintBottom_toBottomOf="@id/guideline_horizontal_bottom"
        app:layout_constraintStart_toStartOf="@id/guideline_vertical_left"
        app:layout_constraintEnd_toEndOf="@id/guideline_vertical_right">

        <com.google.android.material.bottomnavigation.BottomNavigationView
            android:id="@+id/bottom_navigation"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:backgroundTint="@color/navbar_background"
            app:itemIconSize="30sp"
            app:itemIconTint="@color/navbar_icon_color"
            app:itemTextColor="@color/navbar_text_color"
            app:menu="@menu/bottom_nav_menu"
            app:labelVisibilityMode="labeled" />
    </androidx.cardview.widget.CardView>

    <include layout="@layout/loader_overlay" />
    <include layout="@layout/sdk_status_overlay" />
</androidx.constraintlayout.widget.ConstraintLayout>