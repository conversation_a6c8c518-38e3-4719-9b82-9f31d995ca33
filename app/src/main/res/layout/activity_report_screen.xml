<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/platinum"
    android:layout_width="match_parent"
    android:layout_height="match_parent"

    tools:context=".ReportsScreen">


    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_vertical_left"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.04" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_vertical_right"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.96" />


    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_horizontal_top"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.05" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_horizontal_bottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.98" />

    <TextView
        android:id="@+id/report_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="@string/report_title"
        android:textColor="@android:color/black"
        android:textSize="28sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@+id/date_label"
        app:layout_constraintEnd_toStartOf="@+id/guideline_vertical_right"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="@+id/guideline_vertical_left"
        app:layout_constraintTop_toTopOf="@id/guideline_horizontal_top"
        app:layout_constraintVertical_bias="0.100000024" />

    <TextView
        android:id="@+id/date_label"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="@string/date_label"
        android:textSize="15sp"
        app:layout_constraintEnd_toStartOf="@+id/guideline_vertical_right"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="@+id/guideline_vertical_left"
        app:layout_constraintTop_toBottomOf="@id/report_title" />


    <LinearLayout
        android:id="@+id/date_picker_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintBottom_toTopOf="@+id/navbarcardView"
        app:layout_constraintEnd_toStartOf="@+id/guideline_vertical_right"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="@+id/guideline_vertical_left"
        app:layout_constraintTop_toBottomOf="@+id/date_label"
        app:layout_constraintVertical_bias="0.0">

        <TextView
            android:id="@+id/start_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/date_picker_background"
            android:gravity="center"
            android:padding="12dp"
            android:text="@string/start_date"
            android:textColor="@color/black"
            android:textSize="13sp" />

        <TextView
            android:id="@+id/end_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_weight="1"
            android:background="@drawable/date_picker_background"
            android:gravity="center"
            android:padding="12dp"
            android:text="@string/end_date"
            android:textColor="@color/black"
            android:textSize="13sp" />

        <ImageButton
            android:id="@+id/search_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_search" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/summary_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="420dp"
        android:orientation="horizontal"
        app:layout_constraintBottom_toTopOf="@+id/navbarcardView"
        app:layout_constraintEnd_toStartOf="@+id/guideline_vertical_right"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="@+id/guideline_vertical_left"
        app:layout_constraintTop_toBottomOf="@+id/date_picker_layout"
        app:layout_constraintVertical_bias="0.3">


        <LinearLayout
            android:id="@+id/income_layout"
            android:layout_width="135dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:background="@drawable/rounded_purple_background"
            android:gravity="start"
            android:orientation="vertical"
            android:padding="8dp">

            <TextView
                android:id="@+id/income_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/income_label"
                android:textColor="@color/white"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/income_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/income_value"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold" />
        </LinearLayout>


        <LinearLayout
            android:id="@+id/transactions_layout"
            android:layout_width="132dp"
            android:layout_height="wrap_content"
            android:background="@drawable/rounded_purple_background"
            android:gravity="start"
            android:orientation="vertical"
            android:padding="8dp">

            <TextView
                android:id="@+id/transactions_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/transactions_label"
                android:textColor="@color/white"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/transactions_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/transactions_value"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold" />
        </LinearLayout>
    </LinearLayout>

<!--    <TextView-->
<!--        android:id="@+id/today_label"-->
<!--        android:layout_width="0dp"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:layout_marginTop="10dp"-->
<!--        android:text="@string/reports_label"-->
<!--        android:textSize="18sp"-->
<!--        android:padding="12sp"-->
<!--        android:textStyle="bold"-->
<!--        android:textColor="@color/black"-->
<!--        app:layout_constraintEnd_toStartOf="@+id/guideline_vertical_right"-->
<!--        app:layout_constraintHorizontal_bias="0.0"-->
<!--        app:layout_constraintStart_toStartOf="@+id/guideline_vertical_left"-->
<!--        app:layout_constraintTop_toBottomOf="@id/summary_layout" />-->

    <androidx.cardview.widget.CardView
        android:id="@+id/tab_layout_card"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="2dp"
        app:layout_constraintEnd_toEndOf="@id/guideline_vertical_right"
        app:layout_constraintStart_toStartOf="@id/guideline_vertical_left"
        app:layout_constraintTop_toBottomOf="@+id/summary_layout">

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/transaction_tab_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/tab_layout_background"
            app:tabBackground="@drawable/tab_selector"
            app:tabIndicatorHeight="0dp"
            android:layout_marginTop="5sp"
            app:tabIndicatorColor="@android:color/transparent"
            app:tabSelectedTextColor="@android:color/white"
            app:tabTextColor="@color/gray"
            app:tabPaddingStart="5dp"
            app:tabPaddingEnd="5dp"
            app:tabIndicatorFullWidth="true"
            app:tabGravity="fill"
            app:tabMode="fixed"
            app:tabMaxWidth="0dp"
            app:tabRippleColor="@android:color/transparent" />

    </androidx.cardview.widget.CardView>

        <FrameLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="10dp"
            app:layout_constraintBottom_toTopOf="@id/navbarcardView"
            app:layout_constraintEnd_toEndOf="@id/guideline_vertical_right"
            app:layout_constraintStart_toStartOf="@id/guideline_vertical_left"
            app:layout_constraintTop_toBottomOf="@+id/tab_layout_card">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/transaction_recycler_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="false"
                tools:listitem="@layout/item_transaction" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/ecommerce_recycler_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="false"
                android:visibility="gone"
                tools:listitem="@layout/item_ecommerce_transaction" />

            <LinearLayout
                android:id="@+id/empty_state_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="gone">

                <ImageView
                    android:id="@+id/empty_state_image"
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:contentDescription="@string/no_transactions"
                    android:src="@drawable/ic_empty_state" />

                <TextView
                    android:id="@+id/empty_state_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="@string/no_transactions"
                    android:textAppearance="?attr/textAppearanceBody1"
                    android:textColor="?attr/colorOnBackground" />
            </LinearLayout>

        </FrameLayout>

    <androidx.cardview.widget.CardView
        android:id="@+id/navbarcardView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:elevation="8dp"
        app:cardCornerRadius="10dp"
        app:cardBackgroundColor="@color/card_background"
        app:layout_constraintBottom_toBottomOf="@id/guideline_horizontal_bottom"
        app:layout_constraintStart_toStartOf="@id/guideline_vertical_left"
        app:layout_constraintEnd_toEndOf="@id/guideline_vertical_right">

        <com.google.android.material.bottomnavigation.BottomNavigationView
            android:id="@+id/bottom_navigation"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:backgroundTint="@color/navbar_background"
            app:itemIconSize="30sp"
            app:itemIconTint="@color/navbar_icon_color"
            app:itemTextColor="@color/navbar_text_color"
            app:menu="@menu/bottom_nav_menu"
            app:labelVisibilityMode="labeled" />
    </androidx.cardview.widget.CardView>

    <FrameLayout
        android:id="@+id/loading_overlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#99000000"
        android:elevation="10dp"
        android:visibility="gone"
        android:clickable="true"
        android:focusable="true">

        <ProgressBar
            android:id="@+id/progressBar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="123dp"
            android:layout_height="23dp"
            android:layout_gravity="center"
            android:indeterminate="true"
            android:indeterminateTint="@color/green" />
    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
