<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/qr_code_overlay"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#CC000000"
    android:padding="24dp"
    android:visibility="visible">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:elevation="12dp"
        app:cardCornerRadius="24dp"
       >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp"
            android:gravity="center">

            <TextView
                android:id="@+id/qr_instruction_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/scan_this_with_hazelpay_app_to_continue_to_the_transaction_screen"
                android:textColor="@android:color/black"
                android:textSize="16sp"
                android:textStyle="bold"
                android:gravity="center"
                android:paddingBottom="12dp" />

            <ImageView
                android:id="@+id/qr_code_image"
                android:layout_width="220dp"
                android:layout_height="220dp"
                android:layout_marginBottom="12dp"
                android:background="@drawable/rounded_qr_background"
                android:contentDescription="@string/qr_code"
                android:padding="8dp" />

            <TextView
                android:id="@+id/merchant_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/app_name"
                android:textColor="@android:color/black"
                android:textSize="14sp"
                android:textStyle="italic"
                android:gravity="center" />

            <TextView
                android:id="@+id/qr_instruction_text_2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/warning_note"
                android:textColor="@color/lite_red_1"
                android:textSize="12sp"
                android:textStyle="bold"
                android:layout_marginTop="12dp"
                android:gravity="center" />

        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <ImageButton
        android:id="@+id/cancel_button"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_gravity="top|end"
        android:layout_margin="16dp"
        android:background="@drawable/circle_button_background"
        android:contentDescription="@string/cancel"
        android:padding="8dp"
        android:scaleType="centerInside"
        android:src="@android:drawable/ic_menu_close_clear_cancel"
        app:tint="@android:color/white" />

</FrameLayout>
