<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/platinum">

    <!-- Guidelines -->
    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_vertical_left"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.05" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_vertical_right"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.95" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_horizontal_top"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.04" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_horizontal_bottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.98" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/payment_header_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        app:layout_constraintEnd_toEndOf="@id/guideline_vertical_right"
        app:layout_constraintStart_toStartOf="@id/guideline_vertical_left"
        app:layout_constraintTop_toTopOf="@id/guideline_horizontal_top">

        <TextView
            android:id="@+id/payment_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/payment_title"
            android:textColor="@android:color/black"
            android:textSize="28sp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <include
            android:id="@+id/qr_tab_switcher"
            layout="@layout/layout_qr_tab_switcher"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/payment_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/payment_title" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/screen_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:text="@string/screen_title"
        android:textColor="@color/lite_gray"
        android:textSize="12sp"
        app:layout_constraintEnd_toEndOf="@id/guideline_vertical_right"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="@id/guideline_vertical_left"
        app:layout_constraintTop_toBottomOf="@id/payment_header_container" />

    <androidx.cardview.widget.CardView
        android:id="@+id/amount_card"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="6dp"
        app:cardBackgroundColor="@android:color/white"
        app:layout_constraintEnd_toEndOf="@id/guideline_vertical_right"
        app:layout_constraintStart_toStartOf="@id/guideline_vertical_left"
        app:layout_constraintTop_toBottomOf="@id/screen_title">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="16dp"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/currency_symbol"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:textStyle="bold"
                android:textColor="@android:color/black" />

            <EditText
                android:id="@+id/amount_value"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="16dp"
                android:autofillHints=""
                android:hint="@string/enter_amount"
                android:inputType="numberDecimal"
                android:textSize="14sp"
                android:background="@android:color/transparent"
                android:padding="0dp" />

        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <!-- Reference ID Card -->
    <androidx.cardview.widget.CardView
        android:id="@+id/reference_id_card"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="6dp"
        app:cardBackgroundColor="@android:color/white"
        app:layout_constraintEnd_toEndOf="@id/guideline_vertical_right"
        app:layout_constraintStart_toStartOf="@id/guideline_vertical_left"
        app:layout_constraintTop_toBottomOf="@id/amount_card">

        <EditText
            android:id="@+id/reference_id_value"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/reference_id_optional"
            android:textSize="14sp"
            android:background="@android:color/transparent"
            android:padding="16dp" />
    </androidx.cardview.widget.CardView>

    <!-- Select Payment Method -->
    <TextView
        android:id="@+id/select_payment_method_text"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:text="@string/select_payment_method_text"
        android:textSize="18sp"
        android:textColor="@android:color/black"
        app:layout_constraintEnd_toEndOf="@id/guideline_vertical_right"
        app:layout_constraintStart_toStartOf="@id/guideline_vertical_left"
        app:layout_constraintTop_toBottomOf="@id/reference_id_card" />

    <!-- Payment Methods Container -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/payment_methods_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        app:layout_constraintEnd_toEndOf="@id/guideline_vertical_right"
        app:layout_constraintHorizontal_bias="0.01"
        app:layout_constraintStart_toStartOf="@id/guideline_vertical_left"
        app:layout_constraintTop_toBottomOf="@id/select_payment_method_text">

        <ImageButton
            android:id="@+id/payment_method_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@android:color/transparent"
            android:scaleType="centerInside"
            android:src="@drawable/ic_card_payment"
            android:padding="5sp"
            app:layout_constraintEnd_toStartOf="@id/qr_payment_method_icon"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.45" />

        <ImageButton
            android:id="@+id/qr_payment_method_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@android:color/transparent"
            android:scaleType="centerInside"
            android:src="@drawable/ic_qr_code"
            android:padding="5sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/payment_method_icon"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.45" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Pay Button -->

    <ImageButton
        android:id="@+id/next_button"
        android:layout_width="251sp"
        android:layout_height="59dp"
        android:layout_marginStart="32dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="32dp"
        android:layout_marginBottom="140dp"
        android:background="@drawable/button_outline_green"
        android:padding="12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/payment_methods_container"
        app:layout_constraintVertical_bias="0.3" />

    <TextView
        android:id="@+id/next_button_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/next_button_text"
        android:textColor="@color/black"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@+id/next_button"
        app:layout_constraintEnd_toEndOf="@+id/next_button"
        app:layout_constraintStart_toStartOf="@+id/next_button"
        app:layout_constraintTop_toTopOf="@+id/next_button" />

    <!-- Bottom Navigation -->
    <androidx.cardview.widget.CardView
        android:id="@+id/navbarcardView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:elevation="8dp"
        app:cardCornerRadius="10dp"
        app:cardBackgroundColor="@color/card_background"
        app:layout_constraintBottom_toBottomOf="@id/guideline_horizontal_bottom"
        app:layout_constraintStart_toStartOf="@id/guideline_vertical_left"
        app:layout_constraintEnd_toEndOf="@id/guideline_vertical_right">

        <com.google.android.material.bottomnavigation.BottomNavigationView
            android:id="@+id/bottom_navigation"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:backgroundTint="@color/navbar_background"
            app:itemIconSize="30sp"
            app:itemIconTint="@color/navbar_icon_color"
            app:itemTextColor="@color/navbar_text_color"
            app:menu="@menu/bottom_nav_menu"
            app:labelVisibilityMode="labeled" />
    </androidx.cardview.widget.CardView>

    <!-- Fragment container for QR code content -->
    <FrameLayout
        android:id="@+id/qr_code_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        android:background="@color/platinum"
        android:elevation="10dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <include layout="@layout/loader_overlay" />
    <include layout="@layout/sdk_status_overlay" />
</androidx.constraintlayout.widget.ConstraintLayout>