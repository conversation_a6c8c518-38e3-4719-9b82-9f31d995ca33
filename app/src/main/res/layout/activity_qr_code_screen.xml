<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- Background with subtle gradient -->
    <ImageView
        android:id="@+id/background_image"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:src="@drawable/bg_top_shape"
        android:alpha="0.1"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_top"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.03" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_bottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.97" />

    <!-- Back button with improved styling -->
    <ImageButton
        android:id="@+id/back_button"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:background="@drawable/modern_back_button_bg"
        android:contentDescription="@string/cancel"
        android:elevation="4dp"
        android:padding="8dp"
        android:scaleType="centerInside"
        android:src="@android:drawable/ic_menu_close_clear_cancel"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/guideline_top"
        app:tint="@android:color/white" />

    <!-- Header with logo and title -->

    <!-- Main content with card-based design -->

    <LinearLayout
        android:id="@+id/header_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="24dp"
        android:layout_marginTop="88dp"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/guideline_top">

        <ImageView
            android:id="@+id/app_logo"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:background="@drawable/merchantapplogonobg"
            android:contentDescription="@string/qr_code" />

        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:text="@string/app_name"
            android:textColor="@color/black"
            android:textSize="22sp"
            android:textStyle="bold" />
    </LinearLayout>

    <androidx.cardview.widget.CardView
        android:id="@+id/qr_code_card"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="24dp"
        android:layout_marginTop="32dp"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="24dp"
        app:cardElevation="8dp"
        android:foreground="?android:attr/selectableItemBackground"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/header_layout">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- Instruction text with improved styling -->
            <TextView
                android:id="@+id/qr_instruction_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:gravity="center"
                android:text="@string/scan_this_with_hazelpay_app_to_continue_to_the_transaction_screen"
                android:textColor="@color/black"
                android:textSize="16sp"
                android:textStyle="bold" />

            <!-- QR code container with fixed dimensions -->
            <FrameLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:cardCornerRadius="16dp"
                android:elevation="10sp"
                android:layout_marginBottom="24dp">

                <!-- Outer container with fixed size -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_margin="5sp"
                    app:cardCornerRadius="16dp"
                    app:cardBackgroundColor="@color/white">

                    <!-- Inner container for QR code -->
                    <FrameLayout
                        android:layout_width="220sp"
                        android:layout_height="220sp"
                        android:layout_gravity="center"
                        android:background="@drawable/qr_inner_border">

                        <!-- QR code image -->
                        <ImageView
                            android:id="@+id/qr_code_image"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:padding="8dp"
                            android:scaleType="fitCenter"
                            android:contentDescription="@string/qr_code" />
                    </FrameLayout>
                </androidx.cardview.widget.CardView>

                <!-- Progress bar overlay with modern design -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/progress_overlay"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:elevation="12dp"
                    app:cardCornerRadius="16dp"
                    app:cardBackgroundColor="#F5FFFFFF"
                    app:cardElevation="8dp"
                    android:visibility="gone">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="24dp">

                        <FrameLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp">

                            <ImageView
                                android:layout_width="100dp"
                                android:layout_height="100dp"
                                android:src="@drawable/loading_animation" />
                        </FrameLayout>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/processing_payment"
                            android:textColor="@color/lavender"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/loading_dots"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="..."
                            android:textColor="@color/lavender"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:layout_marginTop="4dp" />
                    </LinearLayout>
                </androidx.cardview.widget.CardView>
            </FrameLayout>
        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <!-- Warning note with improved styling -->
    <androidx.cardview.widget.CardView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="24dp"
        android:layout_marginTop="24dp"
        app:cardBackgroundColor="#FFF8F8"
        app:cardCornerRadius="16dp"
        app:cardElevation="4dp"
        android:foreground="?android:attr/selectableItemBackground"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/qr_code_card">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="16dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="12dp"
                android:src="@android:drawable/ic_dialog_info"
                android:contentDescription="@string/warning_note"
                app:tint="@color/lite_red_1" />

            <TextView
                android:id="@+id/qr_instruction_text_2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="start"
                android:text="@string/warning_note"
                android:textColor="@color/lite_red_1"
                android:textSize="14sp"
                android:textStyle="bold" />
        </LinearLayout>
    </androidx.cardview.widget.CardView>
</androidx.constraintlayout.widget.ConstraintLayout>
