<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/platinum"
    android:fitsSystemWindows="true">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_top"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.04" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_left"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.04" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_right"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.96" />

    <!-- QR Code Content -->


    <TextView
        android:id="@+id/back_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:drawablePadding="5sp"
        android:gravity="center"
        android:text="@string/back_to_payment_screen"
        android:textColor="@color/lavender"
        app:drawableStartCompat="@drawable/back_arrow_lavender"
        app:layout_constraintBottom_toTopOf="@+id/qr_code_content"
        app:layout_constraintEnd_toEndOf="@id/guideline_right"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="@id/guideline_left"
        app:layout_constraintTop_toTopOf="@id/guideline_top"
        app:layout_constraintVertical_bias="0.0" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/qr_code_content"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="24dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/guideline_right"
        app:layout_constraintStart_toStartOf="@id/guideline_left"
        app:layout_constraintTop_toBottomOf="@id/back_button">

        <androidx.cardview.widget.CardView
            android:id="@+id/qr_card"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="8dp"
            app:cardBackgroundColor="@color/white"
            app:cardCornerRadius="16dp"
            app:cardElevation="8dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintWidth_percent="0.95"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="20dp">

                <ImageButton
                    android:id="@+id/info_button"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:contentDescription="@string/information"
                    android:padding="8dp"
                    android:src="@drawable/ic_info"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:tint="@color/lavender" />

                <ImageView
                    android:id="@+id/merchant_logo"
                    android:layout_width="55dp"
                    android:layout_height="55dp"
                    android:layout_marginTop="8dp"
                    android:src="@drawable/static_qr_logo"
                    app:tint="@color/lavender"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    android:contentDescription="@string/merchant_logo" />

                <TextView
                    android:id="@+id/merchant_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:textColor="@color/black"
                    android:textSize="22sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/merchant_logo"
                    tools:text="John's Coffee Shop" />

                <TextView
                    android:id="@+id/company_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:textColor="@color/lavender"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/merchant_name"
                    tools:text="ABC Corporation" />

                <TextView
                    android:id="@+id/qr_created_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:textColor="@color/lite_gray"
                    android:textSize="14sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/company_name"
                    tools:text="Created on: May 15, 2025" />

                <View
                    android:id="@+id/divider"
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginTop="12dp"
                    android:layout_marginHorizontal="16dp"
                    android:background="@color/lite_gray"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/qr_created_date" />

                <FrameLayout
                    android:id="@+id/qr_image_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:background="@drawable/rounded_qr_background"
                    android:padding="12dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/divider">

                    <ImageView
                        android:id="@+id/static_qr_image"
                        android:layout_width="280dp"
                        android:layout_height="280dp"
                        android:layout_gravity="center"
                        android:contentDescription="@string/qr_code" />
                </FrameLayout>

                <TextView
                    android:id="@+id/qr_amount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/lavender"
                    android:textSize="22sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/qr_image_container"
                    tools:text="Amount: $10.99" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:gravity="center"
                    android:text="@string/scan_this_qr_code_to_pay"
                    android:textColor="@color/lite_gray"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/qr_amount" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.cardview.widget.CardView>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Empty State -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/empty_state"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="24dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/guideline_right"
        app:layout_constraintStart_toStartOf="@id/guideline_left"
        app:layout_constraintTop_toBottomOf="@id/back_button">

        <androidx.cardview.widget.CardView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="8dp"
            app:cardBackgroundColor="@color/white"
            app:cardCornerRadius="16dp"
            app:cardElevation="8dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintWidth_percent="0.95"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.4">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="24dp">

                <ImageView
                    android:id="@+id/empty_state_icon"
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:src="@drawable/ic_qr_code"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:tint="@color/green" />

                <TextView
                    android:id="@+id/empty_state_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="@string/no_static_qr_codes_yet"
                    android:textColor="@color/black"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/empty_state_icon" />

                <TextView
                    android:id="@+id/empty_state_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:gravity="center"
                    android:text="@string/create_a_static_qr_desc"
                    android:textColor="@color/lite_gray"
                    android:textSize="16sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/empty_state_text" />

                <Button
                    android:id="@+id/create_qr_button"
                    android:layout_width="match_parent"
                    android:layout_height="55dp"
                    android:layout_marginTop="24dp"
                    android:background="@drawable/button_solid_green"
                    android:text="@string/create_qr_code"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/empty_state_description" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.cardview.widget.CardView>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Loading State -->
    <ProgressBar
        android:id="@+id/loading_indicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Info Popover -->
    <androidx.cardview.widget.CardView
        android:id="@+id/info_popover"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:visibility="gone"
        app:cardCornerRadius="16dp"
        app:cardElevation="8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintWidth_percent="0.95"
        app:layout_constraintTop_toBottomOf="@id/back_button">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="24dp">

            <ImageView
                android:id="@+id/info_icon"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_info"
                app:tint="@color/lavender"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:contentDescription="@string/information" />

            <TextView
                android:id="@+id/info_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:text="@string/about_static_qr_codes"
                android:textColor="@color/black"
                android:textSize="20sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/info_icon"
                app:layout_constraintTop_toTopOf="@id/info_icon"
                app:layout_constraintBottom_toBottomOf="@id/info_icon" />

            <View
                android:id="@+id/info_divider"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginTop="16dp"
                android:background="@color/lite_gray"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/info_icon" />

            <TextView
                android:id="@+id/info_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:lineSpacingExtra="4dp"
                android:text="@string/info_about_static_qr"
                android:textColor="@color/black"
                android:textSize="16sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/info_divider" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/close_info_button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:backgroundTint="@color/lavender"
                android:paddingHorizontal="24dp"
                android:text="@string/close"
                android:textColor="@color/white"
                app:cornerRadius="8dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/info_content" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>

    <!-- Create Static QR Dialog -->
    <include
        android:id="@+id/create_static_qr_overlay"
        layout="@layout/dialog_create_static_qr" />

</androidx.constraintlayout.widget.ConstraintLayout>