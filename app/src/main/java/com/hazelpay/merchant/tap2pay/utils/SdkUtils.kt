package com.hazelpay.merchant.tap2pay.utils

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.nfc.Tag
import android.util.Log
import androidx.activity.result.ActivityResultLauncher
import androidx.appcompat.app.AppCompatActivity
import com.ibagroup.tapxphone.sdk.SdkInputStruct
import com.ibagroup.tapxphone.sdk.SdkReturnStruct
import com.ibagroup.tapxphone.sdk.TxpSdkLauncher
import org.json.JSONObject

private const val MERCHANT_BANK_ID = "merchant_bank_id"
private const val APP_LANGUAGE_CODE = "app_language_code"
private const val SOLUTION_PARTNER_ID = "solution_partner_id"
private const val INTENT_TYPE = "intent_type"
private const val CONNECTION_TOKEN = "connection_token"
private const val TOKEN = "token"

private const val EFTAA_PAY = "EftaaPay"

object SdkUtils {

    enum class Interface {
        INTENT,
        JSON_OBJECT,
        SDK_INPUT_STRUCT
    }

    val DEFINE_INTERFACE_MODE: Interface = Interface.SDK_INPUT_STRUCT

    fun startSDKMethod(
        merchantBankId: String,
        appLanguageCode: String,
        solutionPartnerId: String,
        context: Context,
        activityContext: AppCompatActivity,
        callback: (SdkReturnStruct?) -> Unit
    ) {
        when (DEFINE_INTERFACE_MODE) {
            Interface.INTENT -> {
                try {
                    val intent = Intent().apply {
                        putExtra(MERCHANT_BANK_ID, merchantBankId)
                        putExtra(APP_LANGUAGE_CODE, appLanguageCode)
                        putExtra(SOLUTION_PARTNER_ID, solutionPartnerId)
                        putExtra(INTENT_TYPE, "start")
                    }

                    Thread {
                        try {
                            val methodCallResult = TxpSdkLauncher.processIntentSDK(
                                TxpSdkLauncher.intentToJson(intent).toString(),
                                context,
                                activityContext
                            )
                            showMethodResult(methodCallResult, 1)
                            callback(methodCallResult)
                        } catch (e: Exception) {
                            e.printStackTrace()
                            callback(null)
                        }
                    }.start()
                } catch (e: Exception) {
                    e.printStackTrace()
                    callback(null)
                }
            }

            Interface.JSON_OBJECT -> {
                try {
                    val data = JSONObject().apply {
                        put(MERCHANT_BANK_ID, merchantBankId)
                        put(APP_LANGUAGE_CODE, appLanguageCode)
                        put(SOLUTION_PARTNER_ID, solutionPartnerId)
                        put(INTENT_TYPE, "start")
                    }

                    Thread {
                        try {
                            val methodCallResult = TxpSdkLauncher.processIntentSDK(
                                data.toString(),
                                context,
                                activityContext
                            )
                            showMethodResult(methodCallResult, 1)
                            callback(methodCallResult)
                        } catch (e: Exception) {
                            e.printStackTrace()
                            callback(null)
                        }
                    }.start()
                } catch (e: Exception) {
                    e.printStackTrace()
                    callback(null)
                }
            }

            Interface.SDK_INPUT_STRUCT -> {
                try {
                    val data = SdkInputStruct()
                        .setApplicationContext(context)
                        .setActivityContext(activityContext)
                        .setMerchantBankId(merchantBankId)
                        .setAppLanguageCode(appLanguageCode)
                        .setSolutionPartnerId(solutionPartnerId)
                        .setExecutedMethod(TxpSdkLauncher.TxpSdkLauncherMethods.START_SDK)

                    Thread {
                        try {
                            val methodCallResult = TxpSdkLauncher.processSDK(data)
                            showMethodResult(methodCallResult, 1)
                            callback(methodCallResult)
                        } catch (e: Exception) {
                            e.printStackTrace()
                            callback(null)
                        }
                    }.start()
                } catch (e: Exception) {
                    e.printStackTrace()
                    callback(null)
                }
            }
        }
    }

    fun deviceSetupSDKMethod(
        merchantBankId: String,
        appLanguageCode: String,
        solutionPartnerId: String,
        connectionToken: String,
        context: Context,
        callback: (SdkReturnStruct?) -> Unit
    ) {
        when (DEFINE_INTERFACE_MODE) {
            Interface.INTENT -> {
                try {
                    val intent = Intent().apply {
                        putExtra(MERCHANT_BANK_ID, merchantBankId)
                        putExtra(APP_LANGUAGE_CODE, appLanguageCode)
                        putExtra(SOLUTION_PARTNER_ID, solutionPartnerId)
                        putExtra(CONNECTION_TOKEN, connectionToken)
                        putExtra(INTENT_TYPE, "device_setup")
                    }

                    Thread {
                        try {
                            val methodCallResult = TxpSdkLauncher.processIntentSDK(
                                TxpSdkLauncher.intentToJson(intent).toString()
                            )
                            showMethodResult(methodCallResult, 2)
                            callback(methodCallResult)
                        } catch (e: Exception) {
                            e.printStackTrace()
                            callback(null)
                        }
                    }.start()
                } catch (e: Exception) {
                    e.printStackTrace()
                    callback(null)
                }
            }

            Interface.JSON_OBJECT -> {
                try {
                    val data = JSONObject().apply {
                        put(MERCHANT_BANK_ID, merchantBankId)
                        put(APP_LANGUAGE_CODE, appLanguageCode)
                        put(SOLUTION_PARTNER_ID, solutionPartnerId)
                        put(CONNECTION_TOKEN, connectionToken)
                        put(INTENT_TYPE, "device_setup")
                    }

                    Thread {
                        try {
                            val methodCallResult = TxpSdkLauncher.processIntentSDK(data.toString())
                            showMethodResult(methodCallResult, 2)
                            callback(methodCallResult)
                        } catch (e: Exception) {
                            e.printStackTrace()
                            callback(null)
                        }
                    }.start()
                } catch (e: Exception) {
                    e.printStackTrace()
                    callback(null)
                }
            }

            Interface.SDK_INPUT_STRUCT -> {
                try {
                    val data = SdkInputStruct()
                        .setMerchantBankId(merchantBankId)
                        .setAppLanguageCode(appLanguageCode)
                        .setSolutionPartnerId(solutionPartnerId)
                        .setConnectionToken(connectionToken)
                        .setExecutedMethod(TxpSdkLauncher.TxpSdkLauncherMethods.DEVICE_SETUP)

                    Thread {
                        try {
                            val methodCallResult = TxpSdkLauncher.processSDK(data)
                            showMethodResult(methodCallResult, 2)
                            callback(methodCallResult)
                        } catch (e: Exception) {
                            e.printStackTrace()
                            callback(null)
                        }
                    }.start()
                } catch (e: Exception) {
                    e.printStackTrace()
                    callback(null)
                }
            }
        }
    }

    fun stopSDKMethod(
        merchantBankId: String,
        appLanguageCode: String,
        solutionPartnerId: String,
        callback: (SdkReturnStruct?) -> Unit
    ) {
        when (DEFINE_INTERFACE_MODE) {
            Interface.INTENT -> {
                try {
                    val intent = Intent().apply {
                        putExtra(MERCHANT_BANK_ID, merchantBankId)
                        putExtra(APP_LANGUAGE_CODE, appLanguageCode)
                        putExtra(SOLUTION_PARTNER_ID, solutionPartnerId)
                        putExtra(INTENT_TYPE, "stop")
                    }

                    Thread {
                        try {
                            val methodCallResult = TxpSdkLauncher.processIntentSDK(
                                TxpSdkLauncher.intentToJson(intent).toString()
                            )
                            showMethodResult(methodCallResult, 4)
                            callback(methodCallResult)
                        } catch (e: Exception) {
                            e.printStackTrace()
                            callback(null)
                        }
                    }.start()
                } catch (e: Exception) {
                    e.printStackTrace()
                    callback(null)
                }
            }

            Interface.JSON_OBJECT -> {
                try {
                    val data = JSONObject().apply {
                        put(MERCHANT_BANK_ID, merchantBankId)
                        put(APP_LANGUAGE_CODE, appLanguageCode)
                        put(SOLUTION_PARTNER_ID, solutionPartnerId)
                        put(INTENT_TYPE, "stop")
                    }

                    Thread {
                        try {
                            val methodCallResult = TxpSdkLauncher.processIntentSDK(data.toString())
                            showMethodResult(methodCallResult, 4)
                            callback(methodCallResult)
                        } catch (e: Exception) {
                            e.printStackTrace()
                            callback(null)
                        }
                    }.start()
                } catch (e: Exception) {
                    e.printStackTrace()
                    callback(null)
                }
            }

            Interface.SDK_INPUT_STRUCT -> {
                try {
                    val data = SdkInputStruct()
                        .setMerchantBankId(merchantBankId)
                        .setAppLanguageCode(appLanguageCode)
                        .setSolutionPartnerId(solutionPartnerId)
                        .setExecutedMethod(TxpSdkLauncher.TxpSdkLauncherMethods.STOP_SDK)

                    Thread {
                        try {
                            val methodCallResult = TxpSdkLauncher.processSDK(data)
                            showMethodResult(methodCallResult, 4)
                            callback(methodCallResult)
                        } catch (e: Exception) {
                            e.printStackTrace()
                            callback(null)
                        }
                    }.start()
                } catch (e: Exception) {
                    e.printStackTrace()
                    callback(null)
                }
            }
        }
    }

    fun paymentSDKMethod(
        context: Activity,
        activityContext: AppCompatActivity,
        nfcTag: Tag? = null,
        paymentToken: String,
        merchantBankId: String,
        appLanguageCode: String,
        solutionPartnerId: String,
        activityLauncher: ActivityResultLauncher<Intent>,
        callback: (Boolean,SdkReturnStruct?) -> Unit
    ) {
        var isReturningFromPaymentScreen = true
        when (DEFINE_INTERFACE_MODE) {
            Interface.INTENT -> {
                try {
                    val intent = Intent().apply {
                        putExtra(MERCHANT_BANK_ID, merchantBankId)
                        putExtra(APP_LANGUAGE_CODE, appLanguageCode)
                        putExtra(SOLUTION_PARTNER_ID, solutionPartnerId)
                        putExtra(TOKEN, paymentToken)
                        putExtra(INTENT_TYPE, "payment")
                    }

                    Thread {
                        try {
                            val methodCallResult = TxpSdkLauncher.processIntentSDK(
                                TxpSdkLauncher.intentToJson(intent).toString(),
                                context,
                                null,
                                activityLauncher
                            )
                            showMethodResult(methodCallResult,3)
                            callback(isReturningFromPaymentScreen,methodCallResult)
                        } catch (e: Exception) {
                            e.printStackTrace()
                            callback(isReturningFromPaymentScreen,null)
                        }
                    }.start()
                } catch (e: Exception) {
                    e.printStackTrace()
                    callback(isReturningFromPaymentScreen,null)
                }
            }
            Interface.JSON_OBJECT -> {
                try {
                    val data = JSONObject().apply {
                        put(MERCHANT_BANK_ID, merchantBankId)
                        put(APP_LANGUAGE_CODE, appLanguageCode)
                        put(SOLUTION_PARTNER_ID, solutionPartnerId)
                        put(TOKEN, paymentToken)
                        put(INTENT_TYPE, "payment")
                    }

                    Thread {
                        try {
                            val methodCallResult = TxpSdkLauncher.processIntentSDK(
                                data.toString(),
                                context,
                                null,
                                activityLauncher
                            )
                            showMethodResult(methodCallResult,3)
                            callback(isReturningFromPaymentScreen,methodCallResult)
                        } catch (e: Exception) {
                            e.printStackTrace()
                            callback(isReturningFromPaymentScreen,null)
                        }
                    }.start()
                } catch (e: Exception) {
                    e.printStackTrace()
                    callback(isReturningFromPaymentScreen,null)
                }
            }
            Interface.SDK_INPUT_STRUCT -> {
                try {
                    val data = SdkInputStruct()
                        .setApplicationContext(context)
                        .setActivityContext(activityContext)
                        .setActivityResultLauncher(activityLauncher)
                        .setMerchantBankId(merchantBankId)
                        .setAppLanguageCode(appLanguageCode)
                        .setSolutionPartnerId(solutionPartnerId)
                        .setToken(paymentToken)
                        .setExecutedMethod(TxpSdkLauncher.TxpSdkLauncherMethods.PAYMENT)

                    if (nfcTag != null) {
                        data.setCardNfcTag(nfcTag)
                    }

                    Thread {
                        try {
                            val methodCallResult = TxpSdkLauncher.processSDK(data)
                            showMethodResult(methodCallResult,3)
                            callback(isReturningFromPaymentScreen,methodCallResult)
                        } catch (e: Exception) {
                            e.printStackTrace()
                            callback(isReturningFromPaymentScreen,null)
                        }
                    }.start()
                } catch (e: Exception) {
                    e.printStackTrace()
                    callback(isReturningFromPaymentScreen,null)
                }
            }
        }
    }

    fun resetSDKMethod(
        merchantBankId: String,
        appLanguageCode: String,
        solutionPartnerId: String,
        callback: (SdkReturnStruct?) -> Unit
    ) {
        when (DEFINE_INTERFACE_MODE) {
            Interface.INTENT -> {
                try {
                    val intent = Intent().apply {
                        putExtra(MERCHANT_BANK_ID, merchantBankId)
                        putExtra(APP_LANGUAGE_CODE, appLanguageCode)
                        putExtra(SOLUTION_PARTNER_ID, solutionPartnerId)
                        putExtra(INTENT_TYPE, "reset_terminal")
                    }

                    Thread {
                        try {
                            val methodCallResult = TxpSdkLauncher.processIntentSDK(
                                TxpSdkLauncher.intentToJson(intent).toString()
                            )
                            callback(methodCallResult)
                        } catch (e: Exception) {
                            e.printStackTrace()
                            callback(null)
                        }
                    }.start()
                } catch (e: Exception) {
                    e.printStackTrace()
                    callback(null)
                }
            }
            Interface.JSON_OBJECT -> {
                try {
                    val data = JSONObject().apply {
                        put(MERCHANT_BANK_ID, merchantBankId)
                        put(APP_LANGUAGE_CODE, appLanguageCode)
                        put(SOLUTION_PARTNER_ID, solutionPartnerId)
                        put(INTENT_TYPE, "reset_terminal")
                    }

                    Thread {
                        try {
                            val methodCallResult = TxpSdkLauncher.processIntentSDK(data.toString())
                            callback(methodCallResult)
                        } catch (e: Exception) {
                            e.printStackTrace()
                            callback(null)
                        }
                    }.start()
                } catch (e: Exception) {
                    e.printStackTrace()
                    callback(null)
                }
            }
            Interface.SDK_INPUT_STRUCT -> {
                try {
                    val data = SdkInputStruct()
                        .setMerchantBankId(merchantBankId)
                        .setAppLanguageCode(appLanguageCode)
                        .setSolutionPartnerId(solutionPartnerId)
                        .setExecutedMethod(TxpSdkLauncher.TxpSdkLauncherMethods.RESET_TERMINAL)

                    Thread {
                        try {
                            val methodCallResult = TxpSdkLauncher.processSDK(data)
                            callback(methodCallResult)
                            showMethodResult(methodCallResult,3)
                        } catch (e: Exception) {
                            e.printStackTrace()
                            callback(null)
                        }
                    }.start()
                } catch (e: Exception) {
                    e.printStackTrace()
                    callback(null)
                }
            }
        }
    }

    fun checkSDKStatusMethod(
        context: Context,
        activityContext: AppCompatActivity,
        callback: (SdkReturnStruct?) -> Unit
    ) {
        when (DEFINE_INTERFACE_MODE) {
            Interface.SDK_INPUT_STRUCT -> {
                try {
                    val data = SdkInputStruct()
                        .setApplicationContext(context)
                        .setActivityContext(activityContext)
                        .setExecutedMethod(TxpSdkLauncher.TxpSdkLauncherMethods.STATUS)

                    Thread {
                        try {
                            val methodCallResult = TxpSdkLauncher.processSDK(data)
                            showMethodResult(methodCallResult, 6)
                            callback(methodCallResult)
                        } catch (e: Exception) {
                            e.printStackTrace()
                            callback(null)
                        }
                    }.start()
                } catch (e: Exception) {
                    e.printStackTrace()
                    callback(null)
                }
            }
            else -> {
                // For now, only implement SDK_INPUT_STRUCT interface for status checking
                Log.w(EFTAA_PAY, "SDK Status check only supported with SDK_INPUT_STRUCT interface")
                callback(null)
            }
        }
    }

    private fun showMethodResult(methodCallResult: SdkReturnStruct, method: Int) {
        when (method) {
            1 -> {
                Log.i(EFTAA_PAY, "Start SDK: result = $methodCallResult")
            }
            2 -> {
                Log.i(EFTAA_PAY, "Device setup SDK: result = $methodCallResult")
            }
            3 -> {
                Log.i(EFTAA_PAY, "Payment SDK: result = $methodCallResult")
            }
            4 -> {
                Log.i(EFTAA_PAY, "Stop SDK: result = $methodCallResult")
            }
            5 -> {
                Log.i(EFTAA_PAY, "Reset SDK: result = $methodCallResult")
            }
            6 -> {
                Log.i(EFTAA_PAY, "SDK Status: result = $methodCallResult")
            }
        }
    }
}