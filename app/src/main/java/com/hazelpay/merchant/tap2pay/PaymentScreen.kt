package com.hazelpay.merchant.tap2pay

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.FrameLayout
import android.widget.ImageButton
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.cardview.widget.CardView
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.google.android.material.navigation.NavigationBarView
import com.hazelpay.merchant.tap2pay.fragments.StaticQrCodeFragment
import com.hazelpay.merchant.tap2pay.utils.ConnectivityChecker
import com.hazelpay.merchant.tap2pay.utils.enforceOrientation
import com.hazelpay.merchant.tap2pay.utils.getFromSharedPreferences
import com.hazelpay.merchant.tap2pay.utils.getUserParentData
import com.hazelpay.merchant.tap2pay.utils.MerchantType

class PaymentScreen : AppCompatActivity() {
    private lateinit var amountValue: EditText
    private lateinit var referenceIdValue: EditText
    private lateinit var nextButton: ImageButton
    private lateinit var connectivityChecker: ConnectivityChecker
    private lateinit var merchantType: MerchantType

    private var selectedPaymentMethod = 0
    private lateinit var cardMethodIcon: ImageButton
    private lateinit var qrCodeMethodIcon: ImageButton

    private lateinit var qrTabSwitcher: CardView
    private lateinit var dynamicTab: TextView
    private lateinit var staticTab: TextView
    private lateinit var qrCodeContainer: FrameLayout
    private var isStaticQrMode = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_payment_screen)

        enforceOrientation(this)

        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);

        amountValue = findViewById(R.id.amount_value)
        referenceIdValue = findViewById(R.id.reference_id_value)
        nextButton = findViewById(R.id.next_button)
        connectivityChecker = ConnectivityChecker(this)

        val userParentData = getUserParentData()
        cardMethodIcon = findViewById(R.id.payment_method_icon)
        qrCodeMethodIcon = findViewById(R.id.qr_payment_method_icon)
        qrTabSwitcher = findViewById(R.id.qr_tab_switcher)
        dynamicTab = qrTabSwitcher.findViewById(R.id.dynamic_tab)
        staticTab = qrTabSwitcher.findViewById(R.id.static_tab)
        qrCodeContainer = findViewById(R.id.qr_code_container)

        merchantType = MerchantType.fromString(userParentData?.merchantType)

        setupBottomNavigation()
        setupQrTabSwitcher()

        findViewById<TextView>(R.id.currency_symbol).text = userParentData?.currency

        selectedPaymentMethod = 0

        cardMethodIcon.background = null
        qrCodeMethodIcon.background = null

        when (merchantType) {
            MerchantType.ONLINE -> {
                cardMethodIcon.isEnabled = false
                cardMethodIcon.alpha = 0.3f
                qrCodeMethodIcon.isEnabled = true
                qrCodeMethodIcon.alpha = 1.0f

                selectedPaymentMethod = 2
                qrCodeMethodIcon.background = ContextCompat.getDrawable(this, R.drawable.selected_border)

                qrTabSwitcher.visibility = View.VISIBLE
                setDynamicQrMode()
            }
            MerchantType.CARDPRESENT -> {
                cardMethodIcon.isEnabled = true
                cardMethodIcon.alpha = 1.0f
                qrCodeMethodIcon.isEnabled = false
                qrCodeMethodIcon.alpha = 0.3f

                selectedPaymentMethod = 1
                cardMethodIcon.background = ContextCompat.getDrawable(this, R.drawable.selected_border)

                qrTabSwitcher.visibility = View.GONE
            }
            MerchantType.GROUP -> {
                cardMethodIcon.isEnabled = true
                cardMethodIcon.alpha = 1.0f
                qrCodeMethodIcon.isEnabled = true
                qrCodeMethodIcon.alpha = 1.0f
            }
        }

        cardMethodIcon.setOnClickListener {
            if (!cardMethodIcon.isEnabled) return@setOnClickListener

            val imm = getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
            val view = currentFocus
            if (view != null) {
                imm.hideSoftInputFromWindow(view.windowToken, 0)
            }

            selectedPaymentMethod = 1
            cardMethodIcon.background = ContextCompat.getDrawable(this, R.drawable.selected_border)
            qrCodeMethodIcon.background = null

            qrTabSwitcher.visibility = View.GONE
            qrCodeContainer.visibility = View.GONE
        }

        qrCodeMethodIcon.setOnClickListener {
            if (!qrCodeMethodIcon.isEnabled) return@setOnClickListener

            val imm = getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
            val view = currentFocus
            if (view != null) {
                imm.hideSoftInputFromWindow(view.windowToken, 0)
            }

            selectedPaymentMethod = 2
            qrCodeMethodIcon.background = ContextCompat.getDrawable(this, R.drawable.selected_border)
            cardMethodIcon.background = null

            qrTabSwitcher.visibility = View.VISIBLE

            setDynamicQrMode()
        }

        amountValue.setOnFocusChangeListener { _, hasFocus ->
            if (!hasFocus) {
                val input = amountValue.text.toString()
                if (input.isNotEmpty() && !input.contains(".")) {
                    amountValue.setText("$input.00")
                }
            }
        }

        nextButton.setOnClickListener {
            nextButton.isEnabled = false

            val amount = amountValue.text.toString()

            if (amount.isEmpty()) {
                Toast.makeText(this, getString(R.string.enter_valid_amount), Toast.LENGTH_SHORT).show()
                nextButton.isEnabled = true
                return@setOnClickListener
            }

            val amountNumber = amount.toDoubleOrNull()
            if (amountNumber == null || amountNumber <= 0) {
                Toast.makeText(this, getString(R.string.amount_positive_number), Toast.LENGTH_SHORT).show()
                nextButton.isEnabled = true
                return@setOnClickListener
            }

            val decimalRegex = """^\d+\.\d{2}$""".toRegex()
            if (!decimalRegex.matches(amount)) {
                Toast.makeText(this, getString(R.string.amount_two_decimal_places), Toast.LENGTH_SHORT).show()
                nextButton.isEnabled = true
                return@setOnClickListener
            }

            if (selectedPaymentMethod == 0) {
                Toast.makeText(this, getString(R.string.select_payment_method), Toast.LENGTH_SHORT).show()
                nextButton.isEnabled = true
                return@setOnClickListener
            } else if ((merchantType == MerchantType.ONLINE && selectedPaymentMethod == 1) ||
                       (merchantType == MerchantType.CARDPRESENT && selectedPaymentMethod == 2)) {
                Toast.makeText(this, getString(R.string.payment_method_not_available), Toast.LENGTH_SHORT).show()
                nextButton.isEnabled = true
                return@setOnClickListener
            }

            when (selectedPaymentMethod) {
                1 -> {
                    val intent = Intent(this@PaymentScreen, ConfirmationScreen::class.java).apply {
                        putExtra("amount", amount)
                        putExtra("referenceId", referenceIdValue.text.toString())
                        putExtra("selectedPaymentMethod",selectedPaymentMethod.toString())
                    }
                    startActivity(intent)
                    nextButton.isEnabled = true
                }
                2 -> {
                    val intent = Intent(this@PaymentScreen, ConfirmationScreen::class.java).apply {
                        putExtra("selectedPaymentMethod",selectedPaymentMethod.toString())
                        putExtra("amount", amount)
                        putExtra("referenceId", referenceIdValue.text.toString())
                        putExtra("isStaticQrMode", isStaticQrMode)
                    }
                    startActivity(intent)
                    nextButton.isEnabled = true
                }
            }
        }
    }



    override fun onResume() {
        super.onResume()

        selectedPaymentMethod = 0
        cardMethodIcon.background = null
        qrCodeMethodIcon.background = null
        amountValue.text = null

        isStaticQrMode = false
        qrCodeContainer.visibility = View.GONE

        showScreenContent(View.VISIBLE)

        if (merchantType == MerchantType.ONLINE) {
            qrTabSwitcher.visibility = View.VISIBLE
            try {
                dynamicTab.background = ContextCompat.getDrawable(this, R.drawable.selected_tab_background)
                staticTab.background = null
                dynamicTab.setTextColor(ContextCompat.getColor(this, android.R.color.white))
                staticTab.setTextColor(ContextCompat.getColor(this, android.R.color.black))
            } catch (e: Exception) {
                e.printStackTrace()
            }
        } else {
            qrTabSwitcher.visibility = View.GONE
        }
    }

    private fun setupQrTabSwitcher() {
        dynamicTab.setOnClickListener {
            setDynamicQrMode()
        }

        staticTab.setOnClickListener {
            setStaticQrMode()
        }
    }

    fun setDynamicQrMode() {
        isStaticQrMode = false
        try {
            dynamicTab.background = ContextCompat.getDrawable(this, R.drawable.selected_tab_background)
            staticTab.background = null
            dynamicTab.setTextColor(ContextCompat.getColor(this, android.R.color.white))
            staticTab.setTextColor(ContextCompat.getColor(this, android.R.color.black))
        } catch (e: Exception) {
            e.printStackTrace()
        }

        showScreenContent(View.VISIBLE)

        qrCodeContainer.visibility = View.GONE
        amountValue.isEnabled = true
        referenceIdValue.isEnabled = true
    }

    fun setStaticQrMode() {
        isStaticQrMode = true
        try {
            staticTab.background = ContextCompat.getDrawable(this, R.drawable.selected_tab_background)
            dynamicTab.background = null
            staticTab.setTextColor(ContextCompat.getColor(this, android.R.color.white))
            dynamicTab.setTextColor(ContextCompat.getColor(this, android.R.color.black))
        } catch (e: Exception) {
            e.printStackTrace()
        }

        showScreenContent(View.GONE)

        qrCodeContainer.visibility = View.VISIBLE
        amountValue.isEnabled = false
        referenceIdValue.isEnabled = false

        loadStaticQrFragment()
    }

    private fun loadStaticQrFragment() {
        try {
            supportFragmentManager.findFragmentById(R.id.qr_code_container)?.let {
                supportFragmentManager.beginTransaction().remove(it).commit()
                supportFragmentManager.executePendingTransactions()
            }

            val fragment = StaticQrCodeFragment.newInstance()
            supportFragmentManager.beginTransaction()
                .replace(R.id.qr_code_container, fragment)
                .commitNow()
        } catch (e: Exception) {
            e.printStackTrace()
            // Fallback approach
            try {
                val fragment = StaticQrCodeFragment.newInstance()
                supportFragmentManager.beginTransaction()
                    .replace(R.id.qr_code_container, fragment)
                    .commit()
            } catch (e2: Exception) {
                e2.printStackTrace()
            }
        }
    }

    private fun setupBottomNavigation() {
        val bottomNavigationView: BottomNavigationView = findViewById(R.id.bottom_navigation)
        bottomNavigationView.labelVisibilityMode = NavigationBarView.LABEL_VISIBILITY_SELECTED
        bottomNavigationView.selectedItemId = R.id.sales

        bottomNavigationView.setOnItemSelectedListener { item ->
            when (item.itemId) {
                R.id.home -> {
                    startActivity(Intent(this, HomeScreen::class.java))
                    finish()
                    true
                }
                R.id.sales -> {
                    true
                }
                R.id.reports -> {
                    startActivity(Intent(this, ReportsScreen::class.java))
                    finish()
                    true
                }
                R.id.settings -> {
                    startActivity(Intent(this, SettingsScreen::class.java))
                    finish()
                    true
                }
                else -> false
            }
        }

        when (merchantType) {
            MerchantType.ONLINE, MerchantType.GROUP -> {
                bottomNavigationView.menu.findItem(R.id.sales).isEnabled = true
            }
            MerchantType.CARDPRESENT -> {
                val isOpen = getFromSharedPreferences("Terminal_access").toBoolean()
                bottomNavigationView.menu.findItem(R.id.sales).isEnabled = isOpen
            }
        }
    }

    fun showScreenContent(visibility: Int){
        findViewById<androidx.constraintlayout.widget.ConstraintLayout>(R.id.payment_header_container).visibility = visibility
        findViewById<TextView>(R.id.screen_title).visibility = visibility
        findViewById<CardView>(R.id.amount_card).visibility = visibility
        findViewById<CardView>(R.id.reference_id_card).visibility = visibility
        findViewById<TextView>(R.id.select_payment_method_text).visibility = visibility
        findViewById<androidx.constraintlayout.widget.ConstraintLayout>(R.id.payment_methods_container).visibility = visibility
        findViewById<CardView>(R.id.navbarcardView).visibility = visibility
        nextButton.visibility = visibility
    }
}