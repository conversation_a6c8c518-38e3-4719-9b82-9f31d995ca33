package com.hazelpay.merchant.tap2pay

import android.annotation.SuppressLint
import android.app.DatePickerDialog
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.View
import android.view.WindowManager
import android.widget.ImageButton
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.hazelpay.merchant.tap2pay.model.BusinessDayTransaction
import com.hazelpay.merchant.tap2pay.adapter.EcommerceTransactionAdapter
import com.hazelpay.merchant.tap2pay.adapter.EcommerceTransactionItem
import com.hazelpay.merchant.tap2pay.adapter.TransactionAdapter
import com.hazelpay.merchant.tap2pay.adapter.TransactionItem
import com.hazelpay.merchant.tap2pay.utils.ConnectivityChecker
import com.hazelpay.merchant.tap2pay.utils.fetchArchivedTransactionData
import com.hazelpay.merchant.tap2pay.utils.fetchEcommerceTransactionData
import com.hazelpay.merchant.tap2pay.utils.getCurrencySymbolById
import com.hazelpay.merchant.tap2pay.utils.getFromSharedPreferences
import com.hazelpay.merchant.tap2pay.utils.getUserTerminalData
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.google.android.material.navigation.NavigationBarView
import com.google.android.material.tabs.TabLayout
import com.hazelpay.merchant.tap2pay.utils.getUserParentData
import com.hazelpay.merchant.tap2pay.utils.MerchantType
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

class ReportsScreen : AppCompatActivity() {

    private var currentPage = 1
    private var isLoading = false
    private var hasMoreData = true
    private var currentTab = TransactionTab.CARD_PRESENT
    private lateinit var merchantType: MerchantType

    private val cardPresentTransactions = mutableListOf<TransactionItem.Transaction>()
    private val ecommerceTransactions = mutableListOf<EcommerceTransactionItem>()
    private lateinit var cardPresentAdapter: TransactionAdapter
    private lateinit var ecommerceAdapter: EcommerceTransactionAdapter
    private lateinit var tabLayout: TabLayout
    private lateinit var connectivityChecker: ConnectivityChecker

    private enum class TransactionTab {
        CARD_PRESENT, ECOMMERCE
    }


    @SuppressLint("NotifyDataSetChanged")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_report_screen)

        val startDateText: TextView = findViewById(R.id.start_date)
        val endDateText: TextView = findViewById(R.id.end_date)
        val searchButton: ImageButton = findViewById(R.id.search_button)
        connectivityChecker = ConnectivityChecker(this)

        merchantType = MerchantType.fromString(getUserParentData()?.merchantType)

        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

        val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())

        val calendar = Calendar.getInstance()
        val today = calendar.time

        calendar.add(Calendar.DAY_OF_YEAR, -1)
        val yesterday = calendar.time

        startDateText.text = dateFormat.format(yesterday)
        endDateText.text = dateFormat.format(today)

        startDateText.setOnClickListener {
            showDatePicker { selectedDate ->
                startDateText.text = dateFormat.format(selectedDate)
            }
        }

        endDateText.setOnClickListener {
            showDatePicker { selectedDate ->
                endDateText.text = dateFormat.format(selectedDate)
            }
        }

        searchButton.setOnClickListener {
            val startDate = startDateText.text.toString()
            val endDate = endDateText.text.toString()

            if (startDate.isEmpty() || endDate.isEmpty()) {
                Toast.makeText(this, "Please select both start and end dates", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }

            try {
                val parsedStartDate = dateFormat.parse(startDate)
                val parsedEndDate = dateFormat.parse(endDate)

                if (parsedStartDate != null) {
                    if (parsedStartDate.after(parsedEndDate)) {
                        Toast.makeText(this, "Start date cannot be after end date", Toast.LENGTH_SHORT).show()
                        return@setOnClickListener
                    }
                }

                currentPage = 1
                hasMoreData = true

                when (currentTab) {
                    TransactionTab.CARD_PRESENT -> {
                        cardPresentTransactions.clear()
                        cardPresentAdapter.notifyDataSetChanged()
                    }
                    TransactionTab.ECOMMERCE -> {
                        ecommerceTransactions.clear()
                        ecommerceAdapter.notifyDataSetChanged()
                    }
                }

                showLoader()
                fetchTransactionData()

            } catch (_: Exception) {
                Toast.makeText(this, "Invalid date format", Toast.LENGTH_SHORT).show()
            }
        }

        setupTabLayout()
        setupRecyclerView()
        fetchTransactionData()
        setupBottomNavigation()
    }

    private fun showLoader() {
        findViewById<View>(R.id.loading_overlay).visibility = View.VISIBLE
    }

    private fun hideLoader() {
        findViewById<View>(R.id.loading_overlay).visibility = View.GONE
    }

    private fun showDatePicker(onDateSelected: (Date) -> Unit) {
        val calendar = Calendar.getInstance()
        DatePickerDialog(
            this,
            R.style.CustomDatePicker,
            { _, year, month, dayOfMonth ->
                val selectedDate = Calendar.getInstance().apply {
                    set(Calendar.YEAR, year)
                    set(Calendar.MONTH, month)
                    set(Calendar.DAY_OF_MONTH, dayOfMonth)
                }.time
                onDateSelected(selectedDate)
            },
            calendar.get(Calendar.YEAR),
            calendar.get(Calendar.MONTH),
            calendar.get(Calendar.DAY_OF_MONTH)
        ).show()
    }

    private fun setupBottomNavigation() {
        val bottomNavigationView: BottomNavigationView = findViewById(R.id.bottom_navigation)
        bottomNavigationView.labelVisibilityMode = NavigationBarView.LABEL_VISIBILITY_SELECTED
        bottomNavigationView.selectedItemId = R.id.reports

        bottomNavigationView.setOnItemSelectedListener { item ->
            when (item.itemId) {
                R.id.home -> {
                    startActivity(Intent(this, HomeScreen::class.java))
                    finish()
                    true
                }
                R.id.sales -> {
                    startActivity(Intent(this, PaymentScreen::class.java))
                    finish()
                    true
                }
                R.id.reports -> {
                    true
                }
                R.id.settings ->{
                    startActivity(Intent(this, SettingsScreen::class.java))
                    finish()
                    true
                }
                else -> false
            }
        }

        when (merchantType) {
            MerchantType.ONLINE, MerchantType.GROUP -> {
                bottomNavigationView.menu.findItem(R.id.sales).isEnabled = true
            }
            MerchantType.CARDPRESENT -> {
                val isOpen = getFromSharedPreferences("Terminal_access").toBoolean()
                bottomNavigationView.menu.findItem(R.id.sales).isEnabled = isOpen
            }
        }
    }

    private fun setupTabLayout() {
        tabLayout = findViewById(R.id.transaction_tab_layout)

        when (merchantType) {
            MerchantType.ONLINE -> {
                tabLayout.addTab(tabLayout.newTab().setText("Ecommerce"))
                currentTab = TransactionTab.ECOMMERCE
            }
            MerchantType.CARDPRESENT -> {
                tabLayout.addTab(tabLayout.newTab().setText("Card Present"))
                currentTab = TransactionTab.CARD_PRESENT
            }
            MerchantType.GROUP -> {
                tabLayout.addTab(tabLayout.newTab().setText("Card Present"))
                tabLayout.addTab(tabLayout.newTab().setText("Ecommerce"))
            }
        }

        if (tabLayout.tabCount <= 1) {
            tabLayout.visibility = View.GONE
        } else {
            tabLayout.visibility = View.VISIBLE
        }

        tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                showLoader()
                val newTab = when (merchantType) {
                    MerchantType.ONLINE -> TransactionTab.ECOMMERCE
                    MerchantType.CARDPRESENT -> TransactionTab.CARD_PRESENT
                    MerchantType.GROUP -> {
                        when (tab?.position) {
                            0 -> TransactionTab.CARD_PRESENT
                            1 -> TransactionTab.ECOMMERCE
                            else -> TransactionTab.CARD_PRESENT
                        }
                    }
                }

                Log.d("ReportsScreen", "Tab selected: $newTab (position: ${tab?.position})")
                currentTab = newTab
                resetAndFetchTransactions()
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {}
            override fun onTabReselected(tab: TabLayout.Tab?) {
                Log.d("ReportsScreen", "Tab reselected: $currentTab")
                showLoader()
                resetAndFetchTransactions()
            }
        })
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun resetAndFetchTransactions() {
        showLoader()
        currentPage = 1
        hasMoreData = true

        when (currentTab) {
            TransactionTab.CARD_PRESENT -> {
                cardPresentTransactions.clear()
                cardPresentAdapter.notifyDataSetChanged()
            }
            TransactionTab.ECOMMERCE -> {
                ecommerceTransactions.clear()
                ecommerceAdapter.notifyDataSetChanged()
            }
        }

        val cardPresentRecyclerView: RecyclerView = findViewById(R.id.transaction_recycler_view)
        val ecommerceRecyclerView: RecyclerView = findViewById(R.id.ecommerce_recycler_view)

        when (currentTab) {
            TransactionTab.CARD_PRESENT -> {
                cardPresentRecyclerView.visibility = View.VISIBLE
                ecommerceRecyclerView.visibility = View.GONE
            }
            TransactionTab.ECOMMERCE -> {
                cardPresentRecyclerView.visibility = View.GONE
                ecommerceRecyclerView.visibility = View.VISIBLE
            }
        }

        fetchTransactionData()
    }

    private fun fetchTransactionData() {
        val startDate = findViewById<TextView>(R.id.start_date).text.toString()
        val endDate = findViewById<TextView>(R.id.end_date).text.toString()

        when (currentTab) {
            TransactionTab.CARD_PRESENT -> fetchCardPresentTransactions(startDate, endDate)
            TransactionTab.ECOMMERCE -> fetchEcommerceTransactions(startDate, endDate)
        }
    }

    private fun fetchCardPresentTransactions(startDate: String?, endDate: String?) {
        val terminalId = getUserTerminalData()?.terminalId

        if (isLoading) return
        isLoading = true
        showLoader()

        if (terminalId != null) {
            fetchArchivedTransactionData(
                context = this,
                lifecycleScope = lifecycleScope,
                sort = "-date_time",
                startDate = startDate,
                endDate = endDate,
                page = currentPage,
                terminalId
            ) { transactions, paginationData ->
                isLoading = false
                hideLoader()

                val recyclerView: RecyclerView = findViewById(R.id.transaction_recycler_view)
                val emptyStateLayout: LinearLayout = findViewById(R.id.empty_state_layout)
                val txtIncome: TextView = findViewById(R.id.income_value)
                val txtTransaction: TextView = findViewById(R.id.transactions_value)
                val currencySymbol = getUserParentData()?.currency!!

                if (!transactions.isNullOrEmpty()) {
                    recyclerView.visibility = View.VISIBLE
                    emptyStateLayout.visibility = View.GONE

                    val newTransactions = mapTransactions(transactions)
                    cardPresentTransactions.addAll(newTransactions)
                    cardPresentAdapter.notifyItemRangeInserted(
                        cardPresentTransactions.size - newTransactions.size,
                        newTransactions.size
                    )

                    val totalIncome = cardPresentTransactions.filter { it.transactionStatus == 1 && it.originTransactionId == 0 }
                        .sumOf { it.amount.toDouble() }
                    txtIncome.text = "$currencySymbol ${String.format(Locale.US, "%.2f", totalIncome)}"
                    txtTransaction.text = cardPresentTransactions.size.toString()

                    hasMoreData =
                        (paginationData?.currentPage ?: 0) < (paginationData?.pageCount ?: 0)
                } else {
                    hasMoreData = false
                    if (cardPresentTransactions.isEmpty()) {
                        recyclerView.visibility = View.GONE
                        emptyStateLayout.visibility = View.VISIBLE
                        txtIncome.text = "$currencySymbol 0.00"
                        txtTransaction.text = "0"
                    }
                }
            }
        }
    }

    private fun fetchEcommerceTransactions(startDate: String?, endDate: String?) {
        val userId = getUserParentData()?.id

        if (isLoading || userId == null) return
        isLoading = true
        showLoader()

        lifecycleScope.launch {
            fetchEcommerceTransactionData(
                context = this@ReportsScreen,
                page = currentPage,
                userId = userId,
                startDate = startDate,
                endDate = endDate
            ) { transactionList ->
                isLoading = false
                hideLoader()

                val recyclerView: RecyclerView = findViewById(R.id.ecommerce_recycler_view)
                val emptyStateLayout: LinearLayout = findViewById(R.id.empty_state_layout)
                val txtIncome: TextView = findViewById(R.id.income_value)
                val txtTransaction: TextView = findViewById(R.id.transactions_value)
                val currencySymbol = getUserParentData()?.currency!!

                if (transactionList != null && transactionList.transactions.isNotEmpty()) {
                    recyclerView.visibility = View.VISIBLE
                    emptyStateLayout.visibility = View.GONE

                    val newTransactions = transactionList.transactions
                        .filter { it.status != "CREATED" }
                        .map { transaction ->
                        EcommerceTransactionItem(
                            transactionId = transaction.id,
                            externalId = transaction.externalId,
                            amount = (transaction.amount / 100.0).toString(),
                            currencySymbol = transaction.currency ?: currencySymbol,
                            dateTime = transaction.createdAt.toString(),
                            status = transaction.status
                        )
                    }

                    ecommerceTransactions.addAll(newTransactions)
                    ecommerceAdapter.notifyItemRangeInserted(
                        ecommerceTransactions.size - newTransactions.size,
                        newTransactions.size
                    )

                    val totalIncome = ecommerceTransactions
                        .filter { it.status == "CAPTURED" || it.status == "AUTHORISED" }
                        .sumOf { it.amount.toDouble() }
                    txtIncome.text = "$currencySymbol ${String.format(Locale.US, "%.2f", totalIncome)}"
                    txtTransaction.text = ecommerceTransactions.size.toString()

                    hasMoreData = transactionList.paginationCurrentPage.toInt() < transactionList.paginationPageCount.toInt()
                } else {
                    hasMoreData = false
                    if (ecommerceTransactions.isEmpty()) {
                        recyclerView.visibility = View.GONE
                        emptyStateLayout.visibility = View.VISIBLE
                        txtIncome.text = "$currencySymbol 0.00"
                        txtTransaction.text = "0"
                    }
                }
            }
        }
    }

    private fun setupRecyclerView() {
        val cardPresentRecyclerView: RecyclerView = findViewById(R.id.transaction_recycler_view)

        cardPresentAdapter = TransactionAdapter(
            transactions = cardPresentTransactions,
            context = this,
            lifecycleOwner = this,
            isRefund = true,
            refreshCallback = { fetchCardPresentTransactions(null, null) }
        )

        cardPresentRecyclerView.adapter = cardPresentAdapter
        cardPresentRecyclerView.layoutManager = LinearLayoutManager(this)

        cardPresentRecyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)

                if (currentTab == TransactionTab.CARD_PRESENT) {
                    val layoutManager = recyclerView.layoutManager as LinearLayoutManager
                    val totalItemCount = layoutManager.itemCount
                    val lastVisibleItemPosition = layoutManager.findLastVisibleItemPosition()

                    if (!isLoading && hasMoreData && lastVisibleItemPosition + 5 >= totalItemCount) {
                        currentPage++
                        fetchCardPresentTransactions(
                            findViewById<TextView>(R.id.start_date).text.toString(),
                            findViewById<TextView>(R.id.end_date).text.toString()
                        )
                    }
                }
            }
        })

        val ecommerceRecyclerView: RecyclerView = findViewById(R.id.ecommerce_recycler_view)

        ecommerceAdapter = EcommerceTransactionAdapter(
            transactions = ecommerceTransactions,
            context = this
        )

        ecommerceRecyclerView.adapter = ecommerceAdapter
        ecommerceRecyclerView.layoutManager = LinearLayoutManager(this)

        ecommerceRecyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)

                if (currentTab == TransactionTab.ECOMMERCE) {
                    val layoutManager = recyclerView.layoutManager as LinearLayoutManager
                    val totalItemCount = layoutManager.itemCount
                    val lastVisibleItemPosition = layoutManager.findLastVisibleItemPosition()

                    if (!isLoading && hasMoreData && lastVisibleItemPosition + 5 >= totalItemCount) {
                        currentPage++
                        fetchEcommerceTransactions(
                            findViewById<TextView>(R.id.start_date).text.toString(),
                            findViewById<TextView>(R.id.end_date).text.toString()
                        )
                    }
                }
            }
        })

        if (currentTab == TransactionTab.CARD_PRESENT) {
            cardPresentRecyclerView.visibility = View.VISIBLE
            ecommerceRecyclerView.visibility = View.GONE
        } else {
            cardPresentRecyclerView.visibility = View.GONE
            ecommerceRecyclerView.visibility = View.VISIBLE
        }
    }

    private fun mapTransactions(
        transactions: List<BusinessDayTransaction>
    ): List<TransactionItem.Transaction> {
        return transactions.map { transaction ->
            TransactionItem.Transaction(
                transactionId = transaction.id,
                rrn = transaction.rrn,
                cardMask = transaction.card,
                amount = transaction.amount,
                currencySymbol = getCurrencySymbolById(transaction.currency_id),
                dateTime = transaction.date_time,
                transactionStatus = transaction.mpos_transaction_status_id,
                originTransactionId = transaction.origin_trx_id
            )
        }
    }

    override fun onStart() {
        super.onStart()
        connectivityChecker.startListening()
    }

    override fun onStop() {
        super.onStop()
        connectivityChecker.stopListening()
    }
}

