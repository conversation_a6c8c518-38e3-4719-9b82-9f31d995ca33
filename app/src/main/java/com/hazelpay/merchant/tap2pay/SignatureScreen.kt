package com.hazelpay.merchant.tap2pay

import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.widget.ImageButton
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.WindowCompat
import com.hazelpay.merchant.tap2pay.utils.enforceOrientation
import com.github.gcacace.signaturepad.views.SignaturePad

class SignatureScreen : AppCompatActivity() {

    private lateinit var signaturePad: SignaturePad
    private lateinit var clearButton: ImageButton
    private lateinit var proceedButton: ImageButton

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        WindowCompat.setDecorFitsSystemWindows(window, false)
        setContentView(R.layout.activity_signature_screen)
        enforceOrientation(this)

        signaturePad = findViewById(R.id.signature_pad)
        clearButton = findViewById(R.id.clear_button)
        proceedButton = findViewById(R.id.proceed_button)

        signaturePad.setOnSignedListener(object : SignaturePad.OnSignedListener {
            override fun onStartSigning() {

                Toast.makeText(this@SignatureScreen, getString(R.string.signature_started), Toast.LENGTH_SHORT).show()
            }

            override fun onSigned() {

            }

            override fun onClear() {

                Toast.makeText(this@SignatureScreen, getString(R.string.signature_cleared), Toast.LENGTH_SHORT).show()
            }
        })

        clearButton.setOnClickListener {
            signaturePad.clear()
        }

        proceedButton.setOnClickListener {
            if (signaturePad.isEmpty) {

                Toast.makeText(this, getString(R.string.provide_signature), Toast.LENGTH_SHORT).show()
                val intent = Intent(this, TransferFailedScreen::class.java)
                startActivity(intent)
                finish()
            } else {
                val intent = Intent(this, TransferSuccessfulScreen::class.java)
                startActivity(intent)
                finish()
            }
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        enforceOrientation(this)
    }
}
