package com.hazelpay.merchant.tap2pay

import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.view.WindowManager
import android.widget.EditText
import android.widget.ImageButton
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.hazelpay.merchant.tap2pay.utils.PhoneNumberUtils
import com.hazelpay.merchant.tap2pay.utils.enforceOrientation
import java.util.regex.Pattern

class AddCustomerPhoneScreen : AppCompatActivity() {
    private lateinit var phoneNumberEditText: EditText
    private lateinit var confirmButton: ImageButton

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_add_customer_phone_screen)
        enforceOrientation(this)

        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

        phoneNumberEditText = findViewById(R.id.phone_number_edittext)
        confirmButton = findViewById(R.id.confirm_button)

        confirmButton.setOnClickListener {
            val phoneNumber = phoneNumberEditText.text.toString().trim()

            if (isValidPhoneNumber(phoneNumber)) {
                val intent = Intent(this, HomeScreen::class.java)
                startActivity(intent)
                finish()
            } else {
                Toast.makeText(this, getString(R.string.invalid_phone_number), Toast.LENGTH_SHORT).show()
            }
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        enforceOrientation(this)
    }

    private fun isValidPhoneNumber(phoneNumber: String): Boolean {
        val phonePattern = Pattern.compile("^\\+(?:[0-9] ?){6,14}[0-9]$")
        val matcher = phonePattern.matcher(phoneNumber)

        if (!matcher.matches()) {
            return false
        }

        val countryCodeAndNumber = phoneNumber.replace(" ", "")
        val countryCode = PhoneNumberUtils.getCountryCode(countryCodeAndNumber)

        if (countryCode == null) {
            return false
        }

        val localNumber = countryCodeAndNumber.substring(countryCode.length + 1)
        return PhoneNumberUtils.validateLocalNumber(countryCode, localNumber)
    }
}
