package com.hazelpay.merchant.tap2pay.model

import java.time.LocalDateTime

data class EcommerceTransaction(
    val id: String,
    val securityCredentialId: Long,
    val userId: String,
    val status: String,
    val amount: Int,
    val currency: String?,
    val redirectUrl: String?,
    val returnUrl: String?,
    val locale: String?,
    val externalId: String,
    val company: String?,
    val createdAt: LocalDateTime?,
    val updatedAt: LocalDateTime?
)