package com.hazelpay.merchant.tap2pay.utils

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import androidx.appcompat.app.AppCompatActivity

class ConnectivityChecker(private val context: Context) {

    private val connectivityManager =
        context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

    private val networkRequest = NetworkRequest.Builder()
        .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
        .build()

    private val networkCallback = object : ConnectivityManager.NetworkCallback() {
        override fun onAvailable(network: Network) {
            (context as? AppCompatActivity)?.runOnUiThread {
                NetworkOverlayManager.hideOverlay()
            }
        }

        override fun onLost(network: Network) {
            (context as? AppCompatActivity)?.runOnUiThread {
                NetworkOverlayManager.showOverlay(context)
            }
        }
    }

    fun startListening() {
        val activeNetwork = connectivityManager.activeNetwork
        val networkCapabilities = connectivityManager.getNetworkCapabilities(activeNetwork)
        if (networkCapabilities == null || !networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)) {
            (context as? AppCompatActivity)?.runOnUiThread {
                NetworkOverlayManager.showOverlay(context)
            }
        } else {
            (context as? AppCompatActivity)?.runOnUiThread {
                NetworkOverlayManager.hideOverlay()
            }
        }

        connectivityManager.registerNetworkCallback(networkRequest, networkCallback)
    }

    fun stopListening() {
        connectivityManager.unregisterNetworkCallback(networkCallback)
    }

    fun isInternetAvailable(): Boolean {
        val activeNetwork = connectivityManager.activeNetwork
        val networkCapabilities = connectivityManager.getNetworkCapabilities(activeNetwork)
        return networkCapabilities != null && networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
    }
}
