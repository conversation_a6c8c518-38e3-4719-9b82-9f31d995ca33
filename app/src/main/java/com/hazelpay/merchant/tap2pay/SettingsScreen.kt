package com.hazelpay.merchant.tap2pay

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Intent
import android.content.res.Configuration
import android.graphics.Color
import android.os.Bundle
import android.provider.Settings
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.Button
import android.widget.EditText
import android.widget.FrameLayout
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.RadioGroup
import android.widget.Switch
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.cardview.widget.CardView
import com.hazelpay.merchant.tap2pay.utils.ConnectivityChecker
import com.hazelpay.merchant.tap2pay.utils.SdkUtils
import com.hazelpay.merchant.tap2pay.utils.clearAllSharedPreferences
import com.hazelpay.merchant.tap2pay.utils.copyDeviceIdToClipboard
import com.hazelpay.merchant.tap2pay.utils.enforceOrientation
import com.hazelpay.merchant.tap2pay.utils.getFromSharedPreferences
import com.hazelpay.merchant.tap2pay.utils.getUserData
import com.hazelpay.merchant.tap2pay.utils.getUserTerminalData
import com.hazelpay.merchant.tap2pay.utils.isBiometricAvailable
import com.hazelpay.merchant.tap2pay.utils.isBiometricEnabled
import com.hazelpay.merchant.tap2pay.utils.saveToSharedPreferences
import com.hazelpay.merchant.tap2pay.utils.setBiometricEnabled
import com.hazelpay.merchant.tap2pay.utils.showBiometricPrompt
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.google.android.material.navigation.NavigationBarView
import com.google.gson.Gson
import androidx.core.net.toUri
import com.hazelpay.merchant.tap2pay.utils.getUserParentData
import com.hazelpay.merchant.tap2pay.utils.updateUserName
import com.hazelpay.merchant.tap2pay.utils.MerchantType
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlin.toString
import androidx.core.graphics.drawable.toDrawable

class SettingsScreen : AppCompatActivity() {

    private lateinit var biometricSwitch: Switch
    private lateinit var connectivityChecker: ConnectivityChecker
    var isEditing = false
    private lateinit var nameLabel: TextView
    private lateinit var nameEdit: EditText
    private lateinit var editButton: ImageButton
    private lateinit var nameValue: TextView
    private lateinit var merchantType: MerchantType


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_settings_screen)
        enforceOrientation(this)

        connectivityChecker = ConnectivityChecker(this)

        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

        val userData = getUserData()
        val userTerminalData = getUserTerminalData()

        merchantType = MerchantType.fromString(getUserParentData()?.merchantType)

        setupBottomNavigation()

        val deviceId = Settings.Secure.getString(this.contentResolver, Settings.Secure.ANDROID_ID)
        val logoutCard = findViewById<CardView>(R.id.logout_card)
        val termsOfUseCard = findViewById<CardView>(R.id.termsofuse_card)
        val privacyPolicyCard = findViewById<CardView>(R.id.privacypolicy_card)
        val problemCard = findViewById<CardView>(R.id.problem_card)
        val deviceIdentifier = findViewById<TextView>(R.id.device_id_value)
        val terminalIdentifier = findViewById<TextView>(R.id.terminal_id_value)
        val terminalName = findViewById<TextView>(R.id.terminal_name_value)
        val vat = findViewById<CardView>(R.id.vat_card)

        val terminalInfoTitle = findViewById<TextView>(R.id.terminal_info_title)
        val terminalNameCard = findViewById<CardView>(R.id.terminal_name)
        val terminalIdCard = findViewById<CardView>(R.id.terminal_id)

        if (merchantType == MerchantType.ONLINE) {
            terminalInfoTitle.visibility = View.GONE
            terminalNameCard.visibility = View.GONE
            terminalIdCard.visibility = View.GONE
        } else {
            terminalInfoTitle.visibility = View.VISIBLE
            terminalNameCard.visibility = View.VISIBLE
            terminalIdCard.visibility = View.VISIBLE
        }

        nameLabel = findViewById<TextView>(R.id.name_label)
        nameValue = findViewById<TextView>(R.id.name_value)
        nameEdit = findViewById<EditText>(R.id.name_edit)
        editButton = findViewById<ImageButton>(R.id.edit_name_button)

        nameValue.text = userData?.user?.firstName + " " + userData?.user?.lastName

        val isTipEnabled = getFromSharedPreferences("TIP")?.toBoolean() ?: false
        showVAT()

        findViewById<Switch>(R.id.tip_switch).isChecked = isTipEnabled

        deviceIdentifier.text = deviceId
        terminalName.text = userTerminalData?.terminalName
        terminalIdentifier.text = userTerminalData?.terminalId

        editButton.setOnClickListener {
            if (!isEditing) {
                nameLabel.visibility = View.GONE
                nameValue.visibility = View.GONE
                nameEdit.visibility = View.VISIBLE
                nameEdit.setText(nameValue.text)
                editButton.setImageResource(R.drawable.ic_check)
            } else {
                nameLabel.visibility = View.VISIBLE
                nameValue.visibility = View.VISIBLE
                nameEdit.visibility = View.GONE
                nameValue.text = nameEdit.text.toString()
                editButton.setImageResource(R.drawable.ic_edit)

                val fullName = nameEdit.text.toString().trim()
                handleNameUpdate(fullName)
            }
            isEditing = !isEditing
        }

        problemCard.setOnClickListener{
            val intent = Intent(this, SupportScreen::class.java)
            startActivity(intent)
            finish()
        }

        termsOfUseCard.setOnClickListener{
            val url = "https://www.hazelsone.com/termsofuse"
            val intent = Intent(Intent.ACTION_VIEW, url.toUri())
            startActivity(intent)
        }

        privacyPolicyCard.setOnClickListener{
            val url = "https://www.hazelsone.com/privacypolicy"
            val intent = Intent(Intent.ACTION_VIEW, url.toUri())
            startActivity(intent)
        }

        vat.setOnClickListener {
            val inflater = LayoutInflater.from(this)
            val vatOverlay = inflater.inflate(R.layout.layout_vat, null) as FrameLayout

            val dialog = AlertDialog.Builder(this)
                .setView(vatOverlay)
                .create()

            dialog.window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())

            val radioGroup = vatOverlay.findViewById<RadioGroup>(R.id.vat_radio_group)
            val applyButton = vatOverlay.findViewById<Button>(R.id.btn_apply)
            val cancelButton = vatOverlay.findViewById<ImageButton>(R.id.btn_cancel)

            val savedVAT = getFromSharedPreferences("VAT")

            if (!savedVAT.isNullOrEmpty()) {
                when (savedVAT) {
                    getString(R.string.vat_off) -> {
                        radioGroup.check(R.id.rb_off)
                        findViewById<TextView>(R.id.vat_value).text = getString(R.string.vat_off)
                    }
                    getString(R.string.vat_6)   -> {
                        radioGroup.check(R.id.rb_6)
                        findViewById<TextView>(R.id.vat_value).text = getString(R.string.vat_6)
                    }
                    getString(R.string.vat_12)  -> {
                        radioGroup.check(R.id.rb_12)
                        findViewById<TextView>(R.id.vat_value).text = getString(R.string.vat_12)
                    }
                    getString(R.string.vat_25)  -> {
                        radioGroup.check(R.id.rb_25)
                        findViewById<TextView>(R.id.vat_value).text = getString(R.string.vat_25)
                    }
                }

            }else{
                saveToSharedPreferences("VAT", getString(R.string.vat_off))
                radioGroup.check(R.id.rb_off)
                findViewById<TextView>(R.id.vat_value).text = getString(R.string.vat_off)
            }

            cancelButton.setOnClickListener {
                dialog.dismiss()
            }

            applyButton.setOnClickListener {
                val selectedId = radioGroup.checkedRadioButtonId
                if (selectedId != -1) {
                    val selectedOption = when (selectedId) {
                        R.id.rb_off -> getString(R.string.vat_off)
                        R.id.rb_6   -> getString(R.string.vat_6)
                        R.id.rb_12  -> getString(R.string.vat_12)
                        R.id.rb_25  -> getString(R.string.vat_25)
                        else -> ""
                    }
                    saveToSharedPreferences("VAT", selectedOption)
                    findViewById<TextView>(R.id.vat_value).text = selectedOption
                    dialog.dismiss()
                } else {
                    Toast.makeText(this, "Please select a VAT option", Toast.LENGTH_SHORT).show()
                }
            }

            dialog.show()
        }

        findViewById<Switch>(R.id.tip_switch).setOnClickListener{
            saveToSharedPreferences("TIP", findViewById<Switch>(R.id.tip_switch).isChecked.toString())
        }

        deviceIdentifier.setOnClickListener{
            copyDeviceIdToClipboard(this)
        }

        findViewById<ImageView>(R.id.terminal_name_copy).setOnClickListener {
            val textToCopy = terminalName.text.toString()
            copyToClipboard(textToCopy)
        }

        findViewById<ImageView>(R.id.terminal_id_copy).setOnClickListener {
            val textToCopy = terminalIdentifier.text.toString()
            copyToClipboard(textToCopy)
        }

        findViewById<ImageView>(R.id.device_id_copy).setOnClickListener {
            val textToCopy = deviceIdentifier.text.toString()
            copyToClipboard(textToCopy)
        }

        findViewById<TextView>(R.id.address_value).text = userData?.user?.address ?: ""


        biometricSwitch = findViewById(R.id.biometric_switch)
        nameValue.text = listOfNotNull(userData?.user?.firstName, userData?.user?.lastName).joinToString(" ").takeIf { it.isNotBlank() } ?: ""
        findViewById<TextView>(R.id.email_value).text = userData?.user?.email ?: ""
        findViewById<TextView>(R.id.address_value).text = getUserParentData()?.address ?: ""
        findViewById<TextView>(R.id.email_value).text = userData?.user?.email ?: ""


        biometricSwitch.isChecked = isBiometricEnabled()

        biometricSwitch.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                if (isBiometricAvailable(this)) {
                    enableBiometric()
                } else {
                    biometricSwitch.isChecked = false
                    Toast.makeText(
                        this,
                        getString(R.string.biometric_not_available),
                        Toast.LENGTH_SHORT
                    ).show()
                }
            } else {
                disableBiometric()
            }
        }

        logoutCard.setOnClickListener {
            showLogoutConfirmationDialog()
        }


    }

    private fun setupBottomNavigation() {
        val bottomNavigationView: BottomNavigationView = findViewById(R.id.bottom_navigation)
        bottomNavigationView.labelVisibilityMode = NavigationBarView.LABEL_VISIBILITY_SELECTED
        bottomNavigationView.selectedItemId = R.id.settings

        bottomNavigationView.setOnItemSelectedListener { item ->
            when (item.itemId) {
                R.id.home -> {
                    startActivity(Intent(this, HomeScreen::class.java))
                    finish()
                    true
                }
                R.id.sales -> {
                    startActivity(Intent(this, PaymentScreen::class.java))
                    finish()
                    true
                }
                R.id.reports -> {
                    startActivity(Intent(this, ReportsScreen::class.java))
                    finish()
                    true
                }
                R.id.settings -> true
                else -> false
            }
        }

        when (merchantType) {
            MerchantType.ONLINE, MerchantType.GROUP -> {
                bottomNavigationView.menu.findItem(R.id.sales).isEnabled = true
            }
            MerchantType.CARDPRESENT -> {
                val isOpen = getFromSharedPreferences("Terminal_access").toBoolean()
                bottomNavigationView.menu.findItem(R.id.sales).isEnabled = isOpen
            }
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        enforceOrientation(this)
    }

    private fun enableBiometric() {
        showBiometricPrompt(
            context = this,
            onSuccess = {
                setBiometricEnabled(true)
                Toast.makeText(this, getString(R.string.biometric_enabled), Toast.LENGTH_SHORT).show()
            },
            onError = { errorMessage ->
                biometricSwitch.isChecked = false
                Toast.makeText(this, errorMessage, Toast.LENGTH_SHORT).show()
            }
        )
    }

    private fun disableBiometric() {
        setBiometricEnabled(false)
        Toast.makeText(this, getString(R.string.biometric_disabled), Toast.LENGTH_SHORT).show()
    }

    private fun showLogoutConfirmationDialog() {
        AlertDialog.Builder(this).setTitle(getString(R.string.log_out_title))
            .setMessage(getString(R.string.log_out_message))
            .setPositiveButton(getString(R.string.log_out_positive)) { dialog, _ ->
                handleLogout()
                dialog.dismiss()
            }.setNegativeButton(getString(R.string.log_out_negative)) { dialog, _ ->
                dialog.dismiss()
            }.show()
    }

    private fun handleLogout() {
        SdkUtils.stopSDKMethod(
            merchantBankId = BuildConfig.MERCHANT_BANK_ID,
            appLanguageCode = "en",
            solutionPartnerId = BuildConfig.SOLUTION_PARTNER_ID
        ) { }

        val userData = getUserData()
        val isBiometricEnabled = isBiometricEnabled()

        val vat = getFromSharedPreferences("VAT")
        val tip = getFromSharedPreferences("TIP")

        clearAllSharedPreferences()

        saveToSharedPreferences("USER_DATA", Gson().toJson(userData))
        saveToSharedPreferences("BIOMETRIC_ENABLED", isBiometricEnabled.toString())
        saveToSharedPreferences("VAT", vat.toString())
        saveToSharedPreferences("TIP", tip.toString())

        val intent = Intent(this, LoginScreen::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }
        startActivity(intent)
    }


    override fun onStart() {
        super.onStart()
        connectivityChecker.startListening()
    }

    override fun onStop() {
        super.onStop()
        connectivityChecker.stopListening()
    }

    private fun copyToClipboard(text: String) {
        val clipboard = getSystemService(CLIPBOARD_SERVICE) as ClipboardManager
        val clip = ClipData.newPlainText("Copied Text", text)
        clipboard.setPrimaryClip(clip)
    }

    private fun showVAT(){
        val savedVAT = getFromSharedPreferences("VAT")

        if (!savedVAT.isNullOrEmpty()) {
            when (savedVAT) {
                getString(R.string.vat_off) -> {
                    findViewById<TextView>(R.id.vat_value).text = getString(R.string.vat_off)
                }
                getString(R.string.vat_6)   -> {
                    findViewById<TextView>(R.id.vat_value).text = getString(R.string.vat_6)
                }
                getString(R.string.vat_12)  -> {
                    findViewById<TextView>(R.id.vat_value).text = getString(R.string.vat_12)
                }
                getString(R.string.vat_25)  -> {
                    findViewById<TextView>(R.id.vat_value).text = getString(R.string.vat_25)
                }
            }

        }else{
            saveToSharedPreferences("VAT", getString(R.string.vat_off))
            findViewById<TextView>(R.id.vat_value).text = getString(R.string.vat_off)
        }
    }

    private fun handleNameUpdate(fullName: String) {
        val firstSpace = fullName.indexOf(" ")
        val firstName = if (firstSpace != -1) fullName.substring(0, firstSpace) else fullName
        val lastName = if (firstSpace != -1) fullName.substring(firstSpace + 1) else ""

        CoroutineScope(Dispatchers.IO).launch {
            val success = updateUserName(this@SettingsScreen, firstName, lastName)

            withContext(Dispatchers.Main) {
                if (success) {
                    nameLabel.visibility = View.VISIBLE
                    nameValue.visibility = View.VISIBLE
                    nameEdit.visibility = View.GONE
                    nameValue.text = "$firstName $lastName"
                    editButton.setImageResource(R.drawable.ic_edit)
                    Toast.makeText(this@SettingsScreen, "Name updated", Toast.LENGTH_SHORT).show()
                } else {
                    Toast.makeText(this@SettingsScreen, "Failed to update name", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

}