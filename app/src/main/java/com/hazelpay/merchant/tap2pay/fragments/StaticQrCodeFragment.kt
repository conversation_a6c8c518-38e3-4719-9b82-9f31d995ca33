package com.hazelpay.merchant.tap2pay.fragments

import android.graphics.Bitmap
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.EditText
import android.widget.FrameLayout
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import androidx.cardview.widget.CardView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.hazelpay.merchant.tap2pay.PaymentScreen
import com.hazelpay.merchant.tap2pay.R
import com.hazelpay.merchant.tap2pay.model.StaticQrCode
import com.hazelpay.merchant.tap2pay.utils.createStaticQrCode
import com.hazelpay.merchant.tap2pay.utils.generateStaticQrCode
import com.hazelpay.merchant.tap2pay.utils.getStaticQrCodes
import com.hazelpay.merchant.tap2pay.utils.getUserParentData
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Locale

class StaticQrCodeFragment : Fragment() {

    private lateinit var qrCodeContent: ConstraintLayout
    private lateinit var emptyState: ConstraintLayout
    private lateinit var loadingIndicator: ProgressBar
    private lateinit var infoPopover: CardView
    private lateinit var qrCreatedDateText: TextView
    private lateinit var qrAmountText: TextView
    private lateinit var staticQrImage: ImageView
    private lateinit var infoButton: ImageButton
    private lateinit var createQrButton: Button
    private lateinit var closeInfoButton: Button
    private lateinit var backButton: TextView
    private lateinit var merchantName: TextView
    private lateinit var companyName: TextView


    // Create QR Dialog components
    private lateinit var createQrDialog: FrameLayout
    private lateinit var staticQrNameInput: EditText
    private lateinit var staticQrAmountInput: EditText
    private lateinit var staticQrCurrencySymbol: TextView
    private lateinit var createStaticQrButton: Button
    private lateinit var cancelStaticQrButton: Button
    private lateinit var backButtonDialog: ImageButton

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_static_qr_code, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        qrCodeContent = view.findViewById(R.id.qr_code_content)
        emptyState = view.findViewById(R.id.empty_state)
        loadingIndicator = view.findViewById(R.id.loading_indicator)
        infoPopover = view.findViewById(R.id.info_popover)
        qrCreatedDateText = view.findViewById(R.id.qr_created_date)
        qrAmountText = view.findViewById(R.id.qr_amount)
        staticQrImage = view.findViewById(R.id.static_qr_image)
        infoButton = view.findViewById(R.id.info_button)
        createQrButton = view.findViewById(R.id.create_qr_button) as Button
        closeInfoButton = view.findViewById(R.id.close_info_button)
        backButton = view.findViewById(R.id.back_button)
        merchantName = view.findViewById(R.id.merchant_name)
        companyName = view.findViewById(R.id.company_name)

        // Initialize create QR dialog components
        createQrDialog = view.findViewById(R.id.create_static_qr_overlay)
        staticQrNameInput = view.findViewById(R.id.static_qr_name)
        staticQrAmountInput = view.findViewById(R.id.static_qr_amount)
        staticQrCurrencySymbol = view.findViewById(R.id.static_qr_currency_symbol)
        createStaticQrButton = view.findViewById(R.id.create_static_qr_button)
        cancelStaticQrButton = view.findViewById(R.id.cancel_static_qr_button)
        backButtonDialog = view.findViewById(R.id.back_button_dialog)

        // Set currency symbol
        staticQrCurrencySymbol.text = requireContext().getUserParentData()?.currency

        setupListeners()
        loadStaticQrCodes()
    }

    private fun setupListeners() {
        infoButton.setOnClickListener {
            infoPopover.visibility = View.VISIBLE
        }

        closeInfoButton.setOnClickListener {
            infoPopover.visibility = View.GONE
        }

        createQrButton.setOnClickListener {
            showCreateQrDialog()
        }

        cancelStaticQrButton.setOnClickListener {
            hideCreateQrDialog()
        }

        try {
            backButtonDialog.setOnClickListener {
                hideCreateQrDialog()
            }
        } catch (e: Exception) {
            // Handle the case where backButtonDialog might not be initialized
            e.printStackTrace()
        }

        createStaticQrButton.setOnClickListener {
            createStaticQrCode()
        }

        backButton.setOnClickListener {
            // Return to dynamic QR mode
            (activity as? PaymentScreen)?.setDynamicQrMode()
        }
    }

    private fun loadStaticQrCodes() {
        showLoading()

        val parentUserId = requireContext().getUserParentData()?.id
        if (parentUserId == null) {
            showEmptyState()
            return
        }

        viewLifecycleOwner.lifecycleScope.launch {
            val staticQrCodes = getStaticQrCodes(requireContext(), parentUserId)

            if (staticQrCodes.isNullOrEmpty()) {
                showEmptyState()
            } else {
                displayStaticQrCode(staticQrCodes.first())
            }
        }
    }

    private fun displayStaticQrCode(staticQrCode: StaticQrCode) {
        merchantName.text = context?.getUserParentData()?.firstName + " " + context?.getUserParentData()?.lastName
        companyName.text = context?.getUserParentData()?.company

        // Format the date
        val inputFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.getDefault())
        val outputFormat = SimpleDateFormat("MMMM dd, yyyy", Locale.getDefault())
        try {
            val date = inputFormat.parse(staticQrCode.createdAt)
            qrCreatedDateText.text = "Created on: ${outputFormat.format(date!!)}"
        } catch (e: Exception) {
            qrCreatedDateText.text = "Created on: ${staticQrCode.createdAt}"
        }

        // Display amount if available
        if (staticQrCode.defaultAmount != null) {
            val currency = requireContext().getUserParentData()?.currency
            qrAmountText.text = "Amount: $currency ${String.format("%.2f", staticQrCode.defaultAmount)}"
            qrAmountText.visibility = View.VISIBLE
        } else {
            qrAmountText.visibility = View.GONE
        }

        // Generate QR code
        val qrCodeBitmap = generateStaticQrCode(staticQrCode.id)
        staticQrImage.setImageBitmap(qrCodeBitmap)

        showQrCode()
    }

    private fun showLoading() {
        qrCodeContent.visibility = View.GONE
        emptyState.visibility = View.GONE
        loadingIndicator.visibility = View.VISIBLE
        infoPopover.visibility = View.GONE
    }

    private fun showEmptyState() {
        qrCodeContent.visibility = View.GONE
        emptyState.visibility = View.VISIBLE
        loadingIndicator.visibility = View.GONE
        infoPopover.visibility = View.GONE
    }

    private fun showQrCode() {
        qrCodeContent.visibility = View.VISIBLE
        emptyState.visibility = View.GONE
        loadingIndicator.visibility = View.GONE
        infoPopover.visibility = View.GONE
        createQrDialog.visibility = View.GONE
    }

    private fun showCreateQrDialog() {
        createQrDialog.visibility = View.VISIBLE
        staticQrNameInput.setText("")
        staticQrAmountInput.setText("")

        // Make sure the back button is properly connected
        try {
            if (!::backButtonDialog.isInitialized) {
                backButtonDialog = view?.findViewById(R.id.back_button_dialog) ?: return
                backButtonDialog.setOnClickListener {
                    hideCreateQrDialog()
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun hideCreateQrDialog() {
        createQrDialog.visibility = View.GONE
    }

    private fun createStaticQrCode() {
        val name = staticQrNameInput.text.toString().trim()
        val amountStr = staticQrAmountInput.text.toString().trim()

        // Validate inputs
        if (name.isEmpty()) {
            Toast.makeText(requireContext(), "Please enter a name for your QR code", Toast.LENGTH_SHORT).show()
            return
        }

        if (amountStr.isEmpty()) {
            Toast.makeText(requireContext(), "Please enter an amount", Toast.LENGTH_SHORT).show()
            return
        }

        val amount = amountStr.toDoubleOrNull()
        if (amount == null || amount <= 0) {
            Toast.makeText(requireContext(), "Please enter a valid amount", Toast.LENGTH_SHORT).show()
            return
        }

        // Check if amount has exactly 2 decimal places
        val decimalRegex = """^\d+\.\d{2}$""".toRegex()
        if (!decimalRegex.matches(amountStr)) {
            Toast.makeText(requireContext(), "Amount must have exactly 2 decimal places (e.g., 10.99)", Toast.LENGTH_SHORT).show()
            return
        }

        // Get parent user ID
        val parentUserId = requireContext().getUserParentData()?.id
        if (parentUserId == null) {
            Toast.makeText(requireContext(), "Unable to create QR code. Please try again later.", Toast.LENGTH_SHORT).show()
            return
        }

        // Show loading and make API call
        hideCreateQrDialog()
        showLoading()

        viewLifecycleOwner.lifecycleScope.launch {
            val createdQrCode = createStaticQrCode(requireContext(), name, amount, parentUserId)

            if (createdQrCode != null) {
                displayStaticQrCode(createdQrCode)
                Toast.makeText(requireContext(), "Static QR code created successfully", Toast.LENGTH_SHORT).show()
            } else {
                Toast.makeText(requireContext(), "Failed to create static QR code. Please try again.", Toast.LENGTH_SHORT).show()
                loadStaticQrCodes() // Reload existing QR codes
            }
        }
    }

    companion object {
        fun newInstance(): StaticQrCodeFragment {
            return StaticQrCodeFragment()
        }
    }
}
