package com.hazelpay.merchant.tap2pay.adapter

import android.app.AlertDialog
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.ProgressBar
import androidx.recyclerview.widget.RecyclerView
import android.widget.TextView
import android.widget.Toast
import androidx.cardview.widget.CardView
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.hazelpay.merchant.tap2pay.BuildConfig
import com.hazelpay.merchant.tap2pay.R
import com.hazelpay.merchant.tap2pay.utils.convertToUserTimeZone
import com.hazelpay.merchant.tap2pay.utils.processRefund
import com.hazelpay.merchant.tap2pay.utils.reverseTransaction
import kotlinx.coroutines.launch
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit

sealed class TransactionItem {
    data class Transaction(
        val transactionId: Int,
        val rrn: String,
        val cardMask: String,
        val amount: String,
        val currencySymbol: String,
        val dateTime: String,
        var transactionStatus: Int,
        var originTransactionId:Int
    ) : TransactionItem()
}

class TransactionAdapter(
    private val transactions: MutableList<TransactionItem.Transaction>,
    private val context: Context,
    private val lifecycleOwner: androidx.lifecycle.LifecycleOwner,
    private val isRefund: Boolean = false,
    private val refreshCallback: () -> Unit
) : RecyclerView.Adapter<TransactionAdapter.TransactionViewHolder>() {

    inner class TransactionViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val transactionTitle: TextView = itemView.findViewById(R.id.transaction_title)
        val transactionDescription: TextView = itemView.findViewById(R.id.transaction_description)
        val transactionAmount: TextView = itemView.findViewById(R.id.transaction_amount)
        val transactionDateTime: TextView = itemView.findViewById(R.id.transaction_date_time)
        val transactionIcon: ImageView = itemView.findViewById(R.id.transaction_icon)
        val cardView: CardView = itemView.findViewById(R.id.cardView)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TransactionViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_transaction, parent, false)
        return TransactionViewHolder(view)
    }

    override fun onBindViewHolder(holder: TransactionViewHolder, position: Int) {
        val transaction = transactions[position]

        if (transaction.transactionStatus == 1 && transaction.originTransactionId == 0) {
            holder.transactionAmount.setTextColor(ContextCompat.getColor(context, R.color.green))
            holder.transactionIcon.setImageResource(R.drawable.transaction_arrows)
        }else{
            holder.transactionAmount.setTextColor(ContextCompat.getColor(context, R.color.transaction_card_red))
            holder.transactionIcon.setImageResource(R.drawable.transaction_arrows_red)
        }

        holder.transactionTitle.text = transaction.transactionId.toString()
        holder.transactionDescription.text = transaction.cardMask
        holder.transactionAmount.text = context.getString(R.string.transaction_amount_placeholder, transaction.currencySymbol, transaction.amount)
        holder.transactionDateTime.text = context.convertToUserTimeZone(transaction.dateTime)
        holder.cardView.setOnClickListener {
            showTransactionDetailsDialog(transaction)
        }
    }

    override fun getItemCount(): Int = transactions.size

    private fun showTransactionDetailsDialog(transaction: TransactionItem.Transaction) {
        val dialogView = LayoutInflater.from(context).inflate(R.layout.dialog_transaction_details, null)

        val dialog = AlertDialog.Builder(context, R.style.TransparentDialogTheme)
            .setView(dialogView)
            .create()

        val actionButton = dialogView.findViewById<Button>(R.id.reverse_button)
        val cancelButton = dialogView.findViewById<Button>(R.id.close_dialog_button)
        val actionButtonLoader = dialogView.findViewById<ProgressBar>(R.id.reverse_button_loader)

        dialogView.findViewById<TextView>(R.id.dialog_transaction_title).text =
            context.getString(R.string.adapter_transaction_id, transaction.transactionId)
        dialogView.findViewById<TextView>(R.id.dialog_transaction_description).text =
            context.getString(R.string.adapter_card_details, transaction.cardMask)
        dialogView.findViewById<TextView>(R.id.dialog_transaction_amount).text =
            context.getString(R.string.adapter_transaction_amount, transaction.amount, transaction.currencySymbol)
        dialogView.findViewById<TextView>(R.id.dialog_transaction_date).text =
            context.getString(
                R.string.adapter_transaction_date,
                context.convertToUserTimeZone(transaction.dateTime) ?: transaction.dateTime
            )

        if (transaction.originTransactionId != 0) {
            val originId: TextView = dialogView.findViewById(R.id.dialog_transaction_origin_id);
            originId.visibility = View.VISIBLE
            originId.text =
                context.getString(R.string.adapter_reference_id, transaction.originTransactionId)
        }

        if (transaction.transactionStatus == 1 && transaction.originTransactionId == 0) {
            val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
            val transDate = LocalDateTime.parse(transaction.dateTime, formatter)
            val now = LocalDateTime.now()
            val daysDiff = ChronoUnit.DAYS.between(transDate, now)

            if (daysDiff <= 14) {
                actionButton.text = if (isRefund) "REFUND" else "REVERSE"

                actionButton.setOnClickListener {
                    showConfirmationDialog { confirmed ->
                        if (confirmed) {
                            cancelButton.isEnabled = false
                            disableButtonWithLoader(actionButton, actionButtonLoader, dialog)

                            lifecycleOwner.lifecycleScope.launch {
                                if (isRefund) {
                                    processRefund(
                                        context = context,
                                        id = transaction.transactionId,
                                        transactionId = transaction.rrn,
                                        merchantBankId = BuildConfig.MERCHANT_BANK_ID,
                                        amount = BigDecimal(transaction.amount.toDouble())
                                            .setScale(2, RoundingMode.HALF_UP).toDouble()
                                    ) { success, message ->
                                        handleApiResult(success, message, actionButton, actionButtonLoader, dialog, cancelButton, "Refund")
                                    }
                                } else {
                                    reverseTransaction(
                                        context = context,
                                        transactionId = transaction.rrn,
                                        merchantBankId = BuildConfig.MERCHANT_BANK_ID,
                                    ) { success, message ->
                                        handleApiResult(success, message, actionButton, actionButtonLoader, dialog, cancelButton, "Reverse")
                                    }
                                }
                            }
                        }
                    }
                }
            } else {
                actionButton.visibility = View.GONE
                actionButtonLoader.visibility = View.GONE
            }

        } else {
            actionButton.visibility = View.GONE
            actionButtonLoader.visibility = View.GONE
        }

        cancelButton.setOnClickListener {
            dialog.dismiss()
        }

        dialog.show()
    }
    private fun showConfirmationDialog(onResult: (Boolean) -> Unit) {
        val confirmationView = LayoutInflater.from(context).inflate(R.layout.confirmation_dialog, null)
        val confirmationDialog = AlertDialog.Builder(context)
            .setView(confirmationView)
            .setCancelable(false)
            .create()

        confirmationView.findViewById<Button>(R.id.yes_button).setOnClickListener {
            confirmationDialog.dismiss()
            onResult(true)
        }

        confirmationView.findViewById<Button>(R.id.no_button).setOnClickListener {
            confirmationDialog.dismiss()
            onResult(false)
        }

        confirmationDialog.show()
    }

    private fun disableButtonWithLoader(button: Button, loader: ProgressBar, dialog: AlertDialog) {
        button.isEnabled = false
        button.setBackgroundColor(ContextCompat.getColor(context, R.color.gray))
        button.text = ""
        loader.visibility = View.VISIBLE
        dialog.setCancelable(false)
    }

    private fun enableButtonWithLoader(button: Button, loader: ProgressBar, dialog: AlertDialog, actionType: String) {
        button.isEnabled = true
        button.setBackgroundColor(ContextCompat.getColor(context, R.color.green))
        button.text = actionType
        loader.visibility = View.GONE
        dialog.setCancelable(true)
    }

    private fun handleApiResult(
        success: Boolean,
        message:String,
        button: Button,
        loader: ProgressBar,
        dialog: AlertDialog,
        cancelButton: Button,
        actionType: String
    ) {
        enableButtonWithLoader(button, loader, dialog, actionType)
        cancelButton.isEnabled = true
        if (success) {
            Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
            refreshCallback()
            dialog.dismiss()
        } else {
            dialog.dismiss()
            Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
        }
    }
}