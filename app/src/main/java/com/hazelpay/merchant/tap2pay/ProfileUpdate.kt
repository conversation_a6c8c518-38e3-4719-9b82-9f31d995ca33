package com.hazelpay.merchant.tap2pay

import android.content.Intent
import android.content.res.Configuration
import android.net.Uri
import android.os.Bundle
import android.widget.EditText
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import com.hazelpay.merchant.tap2pay.utils.enforceOrientation

class ProfileUpdate : AppCompatActivity() {

    private lateinit var profileImageView: ImageView
    private lateinit var firstNameEditText: EditText
    private lateinit var lastNameEditText: EditText
    private var selectedImageUri: Uri? = null

    private val imagePickerLauncher = registerForActivityResult(
        ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        uri?.let {
            selectedImageUri = it
            profileImageView.setImageURI(it)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_profile_update)
        enforceOrientation(this)


        val setButton = findViewById<ImageButton>(R.id.set_button)
        profileImageView = findViewById(R.id.profile_picture)
        firstNameEditText = findViewById(R.id.first_name)
        lastNameEditText = findViewById(R.id.last_name)

        profileImageView.setOnClickListener {
            openImagePicker()
        }

        setButton.setOnClickListener {
            val firstName = firstNameEditText.text.toString().trim()
            val lastName = lastNameEditText.text.toString().trim()

            if (firstName.isEmpty() || lastName.isEmpty()) {
                Toast.makeText(this, getString(R.string.fill_name_message), Toast.LENGTH_SHORT).show()
            } else {
                val intent = Intent(this, AddPhoneScreen::class.java)
                startActivity(intent)
                finish()
            }
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        enforceOrientation(this)
    }

    private fun openImagePicker() {
        imagePickerLauncher.launch("image/*")
    }
}
