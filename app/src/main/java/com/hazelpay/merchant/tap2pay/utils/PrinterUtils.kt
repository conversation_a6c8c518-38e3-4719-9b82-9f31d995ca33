package com.hazelpay.merchant.tap2pay.utils

import android.content.Context
import android.print.PrintAttributes
import android.print.PrintDocumentAdapter
import android.print.PrintJob
import android.print.PrintManager
import android.webkit.WebView
import android.webkit.WebViewClient
import com.hazelpay.merchant.tap2pay.model.ReceiptData

object PrinterUtils {

    fun printReceipt(
        context: Context,
        receiptData: ReceiptData,
        callback: (Boolean, String?) -> Unit
    ) {
        try {
            // Create HTML content for the receipt
            val htmlContent = generateReceiptHtml(receiptData)

            // Create WebView to render the HTML
            val webView = WebView(context)
            webView.webViewClient = object : WebViewClient() {
                override fun onPageFinished(view: WebView?, url: String?) {
                    super.onPageFinished(view, url)

                    // Create print job
                    createPrintJob(context, webView, callback)
                }
            }

            // Load HTML content
            webView.loadDataWithBaseURL(null, htmlContent, "text/html", "UTF-8", null)

        } catch (e: Exception) {
            callback(false, "Failed to create print preview: ${e.message}")
        }
    }

    private fun createPrintJob(context: Context, webView: WebView, callback: (Boolean, String?) -> Unit) {
        try {
            val printManager = context.getSystemService(Context.PRINT_SERVICE) as PrintManager
            val printAdapter: PrintDocumentAdapter = webView.createPrintDocumentAdapter("Receipt")

            // Set media size to ISO A7
            val printAttributes = PrintAttributes.Builder()
                .setMediaSize(PrintAttributes.MediaSize.ISO_A7)
                .setResolution(PrintAttributes.Resolution("thermal", "Thermal", 600, 600)) // Higher resolution
                .setColorMode(PrintAttributes.COLOR_MODE_MONOCHROME)
                .setMinMargins(PrintAttributes.Margins.NO_MARGINS)
                .build()

            val printJob: PrintJob = printManager.print(
                "Receipt_${System.currentTimeMillis()}",
                printAdapter,
                printAttributes
            )

            callback(true, "Print preview opened successfully")

        } catch (e: Exception) {
            callback(false, "Failed to open print preview: ${e.message}")
        }
    }


    private fun generateReceiptHtml(receiptData: ReceiptData): String {
        return """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <style>
                    @page {
                        size: 80mm auto;
                        margin: 0;
                        -webkit-print-color-adjust: exact;
                    }
                    body {
                        font-family: 'JetBrains Mono', monospace;
                        font-size: 20px;
                        margin: 0;
                        width: 100%;
                        box-sizing: border-box;
                        background: white;
                        -webkit-print-color-adjust: exact;
                    }
                    .center {
                        text-align: center;
                    }
                    .separator {
                        border-top: 3px dashed #000;
                        margin: 12px 0;
                        width: 100%;
                    }
                    .total {
                        font-weight: bold;
                        font-size: 28px;
                        text-align: center;
                        margin: 10px 0;
                    }
                    .below-total {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        font-weight: normal;
                        font-size: 22px;
                        margin: 5px 0;
                    }
                    .status {
                        font-weight: bold;
                        text-align: center;
                        margin: 12px 0;
                        font-size: 24px;
                    }
                    .header-line {
                        text-align: center;
                        margin: 4px 0;
                        font-size: 19px;
                    }
                    .detail-line {
                        margin: 5px 0;
                        word-wrap: break-word;
                        font-size: 16px;
                    }
                    .detail-line-enhanced {
                        margin: 5px 0;
                        word-wrap: break-word;
                        font-size: 18px;
                    }
                    .company-name {
                        font-weight: bold;
                        font-size: 26px;
                    }
                    .footer-text {
                        font-size: 16px;
                        margin: 3px 0;
                        text-align: center;
                    }
                    .amount-line {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin: 5px 0;
                        font-size: 18px;
                    }
                    .amount-line-enhanced {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin: 5px 0;
                        font-size: 18px;
                        font-weight: bold;
                    }
                </style>
            </head>
            <body>
                <div class="center">
                    <div class="header-line">================================</div>
                    <div class="header-line company-name">${receiptData.companyName}</div>
                    <div class="header-line">Company Id: ${receiptData.companyId}</div>
                    <div class="header-line">${receiptData.address}, ${receiptData.city}, ${receiptData.country}, ${receiptData.postalCode}</div>
                    <div class="header-line">================================</div>
                </div>

                <div class="below-total"><span>Receipt #</span><span>${receiptData.transactionId}</span></div>
                <div class="below-total"><span>Order:</span><span> ${receiptData.currency} ${"%.2f".format((receiptData.amount) + (receiptData.vatAmount?.toDouble()!!))}</span></div>

                <div class="separator"></div>

                ${if (!receiptData.tipPercentage.isNullOrEmpty() && receiptData.tipPercentage != "0%")
            "<div class=\"amount-line-enhanced\"><span>Tip (${receiptData.tipPercentage}):</span><span>${receiptData.currency} ${receiptData.tipAmount}</span></div>"
        else ""}

                ${if (!receiptData.vatPercentage.isNullOrEmpty() && receiptData.vatPercentage != "0%")
            "<div class=\"amount-line\"><span>VAT (${receiptData.vatPercentage}):</span><span>${receiptData.currency} ${receiptData.vatAmount}</span></div>"
        else ""}

                <div class="amount-line"><span>Subtotal:</span><span>${receiptData.currency} ${"%.2f".format((receiptData.amount) + (receiptData.vatAmount) + (receiptData.tipAmount?.toDouble()!!))}</span></div>

                <div class="separator"></div>

                <div class="total">TOTAL: ${receiptData.currency} ${"%.2f".format((receiptData.amount) + (receiptData.vatAmount) + (receiptData.tipAmount.toDouble()))}</div>

                <div class="status">${receiptData.status}</div>

                <div class="detail-line">Date/Time: ${receiptData.dateTime}</div>
                <div class="detail-line">Card: ${receiptData.cardMask}</div>
                <div class="detail-line">Payment Method: ${receiptData.paymentMethod}</div>
                <div class="detail-line">MID: ${receiptData.companyId}</div>
                <div class="detail-line">RRN: ${receiptData.rrn}</div>
                <div class="detail-line">Auth Code: ${receiptData.authCode}</div>
                <div class="detail-line">AID: ${receiptData.aid}</div>

                ${if (!receiptData.tvr.isNullOrEmpty()) "<div class=\"detail-line\">TVR: ${receiptData.tvr}</div>" else ""}

                <div class="center">
                    <div class="header-line">================================</div>
                    <div class="header-line">Thank you for your business!</div>
                    <br>
                    <div class="footer-text">Processed by HazelsOne (www.hazelsone.com)</div>
                    <div class="footer-text">A Product of EftaaPay: (www.eftaapay.com)</div>
                    <div class="header-line">================================</div>
                </div>
            </body>
            </html>
        """.trimIndent()
    }
}