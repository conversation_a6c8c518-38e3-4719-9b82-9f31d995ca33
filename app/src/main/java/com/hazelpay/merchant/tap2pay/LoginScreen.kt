package com.hazelpay.merchant.tap2pay

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.content.res.Configuration
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.text.InputType
import android.util.Patterns
import android.view.View
import android.view.WindowManager
import android.widget.Button
import android.widget.EditText
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import android.widget.Toast
import androidx.activity.enableEdgeToEdge
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.hazelpay.merchant.tap2pay.utils.ConnectivityChecker
import com.hazelpay.merchant.tap2pay.utils.authenticateWithBiometrics
import com.hazelpay.merchant.tap2pay.utils.enforceOrientation
import com.hazelpay.merchant.tap2pay.utils.getUserData
import com.hazelpay.merchant.tap2pay.utils.isBiometricEnabled
import com.hazelpay.merchant.tap2pay.utils.isSessionValid
import com.hazelpay.merchant.tap2pay.utils.loginUser
import kotlinx.coroutines.launch

class LoginScreen : AppCompatActivity() {
    private lateinit var connectivityChecker: ConnectivityChecker

    companion object {
        private const val PERMISSION_REQUEST_CODE = 1
    }

    @RequiresApi(Build.VERSION_CODES.P)
    private val requiredPermissions = listOf(
        Manifest.permission.NFC,
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.ACCESS_COARSE_LOCATION,
        Manifest.permission.USE_BIOMETRIC
    )
    private var isPermissionRequestShown = false

    @RequiresApi(Build.VERSION_CODES.P)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        enforceOrientation(this)
        setContentView(R.layout.activity_login_screen)

        requestRequiredPermissions()

        connectivityChecker = ConnectivityChecker(this)

        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

        val userData = getUserData()
        val emailEditText = findViewById<EditText>(R.id.et_email)
        val loginButton = findViewById<LinearLayout>(R.id.ll_login_btn)
        val biometricButton = findViewById<ImageView>(R.id.image_fingerprint)
        val signupButton = findViewById<TextView>(R.id.text_merchant_link)
        val etPassword = findViewById<EditText>(R.id.et_password)
        val ivPasswordToggle = findViewById<ImageView>(R.id.iv_password_toggle)

        if (isBiometricEnabled() && userData != null && isSessionValid(this) && connectivityChecker.isInternetAvailable()) {
            showBiometricPrompt()
        }

        fun validateInput(email: String, password: String): Boolean {
            if (email.isEmpty()) {
                Toast.makeText(this, getString(R.string.error_email_empty), Toast.LENGTH_SHORT).show()
                return false
            }
            if (!Patterns.EMAIL_ADDRESS.matcher(email).matches()) {
                Toast.makeText(this, getString(R.string.error_invalid_email), Toast.LENGTH_SHORT).show()
                return false
            }
            if (password.isEmpty()) {
                Toast.makeText(this, getString(R.string.error_empty_password), Toast.LENGTH_SHORT).show()
                return false
            }
            return true
        }

        signupButton.setOnClickListener{
            val intent = Intent(this@LoginScreen, OnboardingForm::class.java)
            startActivity(intent)
            finish()
        }

        ivPasswordToggle.setOnClickListener {
            if (etPassword.inputType == (InputType.TYPE_CLASS_TEXT or InputType.TYPE_TEXT_VARIATION_PASSWORD)) {
                etPassword.inputType = InputType.TYPE_CLASS_TEXT or InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD
                ivPasswordToggle.setImageResource(R.drawable.ic_visibility)
            } else {
                etPassword.inputType = InputType.TYPE_CLASS_TEXT or InputType.TYPE_TEXT_VARIATION_PASSWORD
                ivPasswordToggle.setImageResource(R.drawable.ic_visibility_off)
            }
            etPassword.setSelection(etPassword.text.length)
        }

        biometricButton.setOnClickListener {
            showBiometricPrompt()
        }

        loginButton.setOnClickListener {
            showLoader()
            val email = emailEditText.text.toString()
            val password = etPassword.text.toString()

            if (validateInput(email, password)) {
                lifecycleScope.launch {
                    val loginResponse = loginUser(this@LoginScreen, email, password)
                    if(loginResponse != null && loginResponse.data?.user?.parentId == null){
                        hideLoader()
                        Toast.makeText(
                            this@LoginScreen,
                            getString(R.string.admin_not_allowed),
                            Toast.LENGTH_SHORT
                        ).show()
                        return@launch
                    }
                    if (loginResponse != null && loginResponse.status) {
                        Toast.makeText(
                            this@LoginScreen,
                            getString(R.string.login_success),
                            Toast.LENGTH_SHORT
                        ).show()
                        hideLoader()
                        val intent = Intent(this@LoginScreen, HomeScreen::class.java)
                        startActivity(intent)
                        finish()
                    } else {
                        hideLoader()
                        Toast.makeText(
                            this@LoginScreen,
                            loginResponse?.message ?: getString(R.string.error_login_failed),
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                }
            } else {
                hideLoader()
            }
        }
    }

    @RequiresApi(Build.VERSION_CODES.P)
    override fun onResume() {
        super.onResume()
        checkPermissionsAndUpdateUI()
    }

    override fun onStart() {
        super.onStart()
        connectivityChecker.startListening()
    }

    override fun onStop() {
        super.onStop()
        connectivityChecker.stopListening()
    }

    @RequiresApi(Build.VERSION_CODES.P)
    private fun requestRequiredPermissions() {
        val permissionsToRequest = requiredPermissions.filter {
            ContextCompat.checkSelfPermission(this, it) != PackageManager.PERMISSION_GRANTED
        }

        if (permissionsToRequest.isNotEmpty()) {
            isPermissionRequestShown = true
            ActivityCompat.requestPermissions(
                this,
                permissionsToRequest.toTypedArray(),
                PERMISSION_REQUEST_CODE
            )
        }
    }

    @RequiresApi(Build.VERSION_CODES.P)
    private fun checkPermissionsAndUpdateUI() {
        val permissionsToRequest = requiredPermissions.filter {
            ContextCompat.checkSelfPermission(this, it) != PackageManager.PERMISSION_GRANTED
        }

        if (permissionsToRequest.isNotEmpty()) {
            if (isPermissionRequestShown) {
                showPermissionOverlay()
            }
        } else {
            hidePermissionOverlay()
            isPermissionRequestShown = false
        }
    }

    @RequiresApi(Build.VERSION_CODES.P)
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == PERMISSION_REQUEST_CODE) {
            checkPermissionsAndUpdateUI()
        }
    }

    private fun showPermissionOverlay() {
        val permissionOverlay = findViewById<RelativeLayout>(R.id.dialog_permission_required)
        val permissionOverlayBG = findViewById<FrameLayout>(R.id.dialog_permission_required_overlay)

        permissionOverlayBG.visibility = View.VISIBLE
        permissionOverlay.visibility = View.VISIBLE

        val openSettingsButton = permissionOverlay.findViewById<Button>(R.id.open_settings_button)
        openSettingsButton.setOnClickListener {
            openAppSettings()
            permissionOverlay.visibility = View.GONE
        }

        val exitButton = permissionOverlay.findViewById<Button>(R.id.exit_app_button)
        exitButton.setOnClickListener {
            finish()
        }
    }

    private fun hidePermissionOverlay() {
        val permissionOverlay = findViewById<RelativeLayout>(R.id.dialog_permission_required)
        val permissionOverlayBG = findViewById<FrameLayout>(R.id.dialog_permission_required_overlay)

        permissionOverlayBG.visibility = View.GONE
        permissionOverlay.visibility = View.GONE
    }

    private fun openAppSettings() {
        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
            data = Uri.fromParts("package", packageName, null)
        }
        startActivity(intent)
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        enforceOrientation(this)
    }

    private fun showBiometricPrompt() {
        authenticateWithBiometrics(
            context = this,
            onSuccess = {
                val intent = Intent(this, HomeScreen::class.java)
                startActivity(intent)
                finish()
            },
            onFailure = { errorMessage ->
                Toast.makeText(this, errorMessage, Toast.LENGTH_SHORT).show()
            }
        )
    }

    private fun showLoader() {
        findViewById<View>(R.id.loading_overlay).visibility = View.VISIBLE
    }

    private fun hideLoader() {
        findViewById<View>(R.id.loading_overlay).visibility = View.GONE
    }
}