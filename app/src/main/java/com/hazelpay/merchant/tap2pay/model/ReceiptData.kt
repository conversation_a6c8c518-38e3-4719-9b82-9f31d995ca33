
package com.hazelpay.merchant.tap2pay.model

data class ReceiptData(
    // Merchant Information
    val companyName: String,
    val companyId: String,
    val address: String,
    val postalCode: String,
    val city: String,
    val country: String,
    val transactionId: String,
    val amount: Double,
    val currency: String,
    val dateTime: String,
    val cardMask: String,
    val paymentMethod: String,
    val rrn: String,
    val authCode: String,
    val aid: String,
    val tvr: String?,

    // VAT and Tip Information
    val vatPercentage: String?,
    val vatAmount: Double?,
    val tipPercentage: String?,
    val tipAmount: Double?,
    val subtotal: String,
    val total: String,

    // Terminal Information
    val terminalId: String,
    val merchantId: String,

    val status: String
)