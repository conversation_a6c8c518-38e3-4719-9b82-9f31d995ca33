package com.hazelpay.merchant.tap2pay.utils

import com.hazelpay.merchant.tap2pay.network.ApiService
import android.content.Context
import android.util.Log
import com.hazelpay.merchant.tap2pay.model.ApiResponse
import com.hazelpay.merchant.tap2pay.model.AuthRequest
import com.hazelpay.merchant.tap2pay.model.BusinessDayRequest
import com.hazelpay.merchant.tap2pay.model.BusinessDayResponse
import com.hazelpay.merchant.tap2pay.model.BusinessDayTransaction
import com.hazelpay.merchant.tap2pay.model.PaginationData
import com.hazelpay.merchant.tap2pay.model.PaymentData
import com.hazelpay.merchant.tap2pay.model.PaymentRequest
import com.hazelpay.merchant.tap2pay.model.RefundRequestPayload
import com.hazelpay.merchant.tap2pay.model.ReverseTransactionPayload
import com.hazelpay.merchant.tap2pay.model.TransactionRequest
import com.hazelpay.merchant.tap2pay.model.LoginData
import com.hazelpay.merchant.tap2pay.model.LoginRequest
import com.hazelpay.merchant.tap2pay.model.OnboardingRequest
import com.hazelpay.merchant.tap2pay.model.PaymentReceipt
import com.hazelpay.merchant.tap2pay.model.PedStatusRequest
import com.hazelpay.merchant.tap2pay.model.PedStatusResponse
import com.hazelpay.merchant.tap2pay.model.SupportRequest
import com.hazelpay.merchant.tap2pay.model.TerminalParentData
import com.hazelpay.merchant.tap2pay.model.TransactionReceiptRequest
import com.hazelpay.merchant.tap2pay.network.RetrofitClientEftaapay
import com.google.gson.Gson
import com.hazelpay.merchant.tap2pay.BuildConfig
import com.hazelpay.merchant.tap2pay.R
import com.hazelpay.merchant.tap2pay.model.EcommerceRequest
import com.hazelpay.merchant.tap2pay.model.EcommerceTransactionList
import com.hazelpay.merchant.tap2pay.model.QrCodeDetails
import com.hazelpay.merchant.tap2pay.model.SecurityCredentialData
import com.hazelpay.merchant.tap2pay.model.StaticQrCode
import com.hazelpay.merchant.tap2pay.model.StaticQrCodeRequest
import com.hazelpay.merchant.tap2pay.model.UpdateUserRequest
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONException
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale

private var MerchantService: ApiService =
    RetrofitClientEftaapay.instance.create(ApiService::class.java)

suspend fun fetchAuthToken(context: Context): String? {
    return try {
        val authRequest = AuthRequest(
            username = BuildConfig.AUTH_USERNAME,
            password = BuildConfig.AUTH_PASSWORD
        )

        val authResponse = MerchantService.authenticate(authRequest)

        if (authResponse.status) {
            val authToken = authResponse.data
            if (authToken != null) {
                context.saveToSharedPreferences("AUTHORIZATION_TOKEN", authToken)
            }
            authToken
        } else {
            Log.e("API_ERROR", "Authentication failed: ${authResponse.message}")
            null
        }
    } catch (e: Exception) {
        e.printStackTrace()
        Log.e("API_ERROR", "Error fetching auth token: ${e.message}")
        null
    }
}

suspend fun fetchConnectionToken(
    context: Context,
    authToken: String,
    terminalId: String,
    merchantId: String,
    action: String
): Pair<Boolean, String?> {
    return try {
        val response = MerchantService.bindDevice(
            "Bearer $authToken",
            mapOf(
                "merchant_bank_id" to BuildConfig.MERCHANT_BANK_ID,
                "merchant_id_type" to "merchant_email",
                "merchant_id" to merchantId,
                "merchant_terminal_id" to terminalId,
                "merchant_device_id" to "",
                "app_language_code" to "en",
                "action" to action
            )
        )

        if (response.data?.token != null) {
            val connectionToken = response.data.token
            val connectionTokenExpiry = response.data.expired_time

            context.saveToSharedPreferences("CONNECTION_TOKEN", connectionToken)
            context.saveToSharedPreferences("CONNECTION_TOKEN_EXPIRY", connectionTokenExpiry)

            Log.d("CONNECTION_TOKEN", "Token: $connectionToken")
            Log.d("CONNECTION_TOKEN_EXPIRY", "Expiry: $connectionTokenExpiry")

            Pair(true, connectionToken)
        } else {
            Log.e("API_ERROR", "Failed API response: $response")
            Pair(false, null)
        }
    } catch (e: Exception) {
        Log.e("API_ERROR", "Exception: ${e.message}", e)
        Pair(false, null)
    }
}

fun fetchTransactionDataForCurrentDay(
    context: Context,
    lifecycleScope: androidx.lifecycle.LifecycleCoroutineScope,
    sort: String,
    page: Int,
    terminalId: String,
    callback: (List<BusinessDayTransaction>?, PaginationData?) -> Unit
) {
    lifecycleScope.launch {
        try {
            val authToken = fetchAuthToken(context)
            if (authToken == null) {
                callback(null, null)
                return@launch
            }

            val currenciesFetched = fetchAndStoreCurrencies(context, authToken)
            if (!currenciesFetched) {
                callback(null, null)
                return@launch
            }

            val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
            val calendar = Calendar.getInstance()

            val currentDate = dateFormat.format(calendar.time)
            val dateTimeRange = "$currentDate - $currentDate"

            val transactionRequest = TransactionRequest(
                page = page,
                sort = sort,
                dateTimeRange = dateTimeRange,
                paymentTerminalId = terminalId
            )

            val transactionResponse = MerchantService.getCurrentDayTransactions(
                authorization = "Bearer $authToken",
                transactionRequest = transactionRequest
            )

            if (transactionResponse.status) {
                val transactions = transactionResponse.data.transactions
                val paginationData = PaginationData(
                    totalCount = transactionResponse.data.paginationTotalCount.toInt(),
                    pageCount = transactionResponse.data.paginationPageCount.toInt(),
                    currentPage = transactionResponse.data.paginationCurrentPage.toInt(),
                    perPage = transactionResponse.data.paginationPerPage.toInt()
                )
                callback(transactions, paginationData)
            } else {
                callback(null, null)
            }
        } catch (e: Exception) {
            e.printStackTrace()
            Log.e("API_ERROR", "Error fetching transaction data: ${e.message}")
            callback(null, null)
        }
    }
}

suspend fun fetchAndStoreCurrencies(context: Context, authToken: String): Boolean {
    return try {
        val currencyResponse = MerchantService.getCurrencyDetails("Bearer $authToken")

        val gson = Gson()
        val currencyJson = gson.toJson(currencyResponse)

        context.saveToSharedPreferences("CURRENCY_DATA", currencyJson)
        true
    } catch (e: Exception) {
        e.printStackTrace()
        Log.e("API_ERROR", "Error fetching currencies: ${e.message}")
        false
    }
}

fun fetchArchivedTransactionData(
    context: Context,
    lifecycleScope: androidx.lifecycle.LifecycleCoroutineScope,
    sort: String,
    startDate: String? = null,
    endDate: String? = null,
    page: Int,
    terminalId: String,
    callback: (List<BusinessDayTransaction>?, PaginationData?) -> Unit
) {
    lifecycleScope.launch {
        try {
            val authToken = fetchAuthToken(context)
            if (authToken == null) {
                callback(null, null)
                return@launch
            }

            val currenciesFetched = fetchAndStoreCurrencies(context, authToken)
            if (!currenciesFetched) {
                callback(null, null)
                return@launch
            }

            val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
            val calendar = Calendar.getInstance()

            val defaultEndDate = dateFormat.format(calendar.time)
            calendar.add(Calendar.DAY_OF_YEAR, -1)
            val defaultStartDate = dateFormat.format(calendar.time)

            val finalStartDate = startDate ?: defaultStartDate
            val finalEndDate = endDate ?: defaultEndDate
            val dateTimeRange = "$finalStartDate - $finalEndDate"

            val request = TransactionRequest(
                page = page,
                sort = sort,
                dateTimeRange = dateTimeRange,
                paymentTerminalId = terminalId
            )

            val response = MerchantService.fetchArchivedTransactions(
                authorization = "Bearer $authToken",
                request = request
            )

            if (response.status) {
                val transactions = response.data.transactions
                val paginationData = PaginationData(
                    totalCount = response.data.paginationTotalCount.toInt(),
                    pageCount = response.data.paginationPageCount.toInt(),
                    currentPage = response.data.paginationCurrentPage.toInt(),
                    perPage = response.data.paginationPerPage.toInt()
                )
                callback(transactions, paginationData)
            } else {
                callback(null, null)
            }
        } catch (e: Exception) {
            e.printStackTrace()
            Log.e("API_ERROR", "Error fetching archived transaction data: ${e.message}")
            callback(null, null)
        }
    }
}

suspend fun reverseTransaction(
    context: Context,
    transactionId: String,
    merchantBankId: String,
    callback: (Boolean, String) -> Unit
) {
    try {
        val authToken = fetchAuthToken(context)
        if (authToken == null) {
            Log.e("API_ERROR", "Auth token is null")
            callback(false, "An Unexpected Error Occurred, Please Try Again Later")
            return
        }

        val payload = ReverseTransactionPayload(
            merchant_bank_id = merchantBankId,
            transaction_id_type = "rrn",
            transaction_id = transactionId
        )

        val response = MerchantService.reverseTransaction(
            "Bearer $authToken",
            payload
        )

        if (response.isSuccessful) {
            Log.d("API_SUCCESS", "Transaction reversed successfully")
            callback(true, "Reverse Successful")
        } else {
            Log.e("API_ERROR", "Failed to reverse transaction: ${response.code()}")
            callback(false, "Failed to reverse transaction: ${response.code()}")

        }
    } catch (e: Exception) {
        e.printStackTrace()
        Log.e("API_ERROR", "Error reversing transaction: ${e.message}")
        callback(false, "An Unexpected Error Occurred, Please Try Again Later")
    }
}

suspend fun processRefund(
    context: Context,
    id: Int,
    transactionId: String,
    merchantBankId: String,
    amount: Double,
    callback: (Boolean, String) -> Unit
) {
    try {
        val authToken = fetchAuthToken(context)
        if (authToken.isNullOrEmpty()) {
            Log.e("API_ERROR", "Auth token is null or empty")
            callback(false, "An Unexpected Error Occurred, Please Try Again Later")
            return
        }

        val payload = RefundRequestPayload(
            id = id,
            merchant_bank_id = merchantBankId,
            transaction_id = transactionId,
            amount = amount
        )

        val response = MerchantService.processRefund(
            "Bearer $authToken",
            payload
        )

        if (response.isSuccessful) {
            Log.d("API_SUCCESS", "Refund processed successfully")
            callback(true, "Refund Successful")
        } else {
            val errorStr = response.errorBody()?.string() ?: ""
            Log.e("API_ERROR", "Error body: $errorStr")
            try {
                val outer = JSONObject(errorStr)
                val outerMsg = outer.getString("message")
                val nestedMsg =
                    JSONObject(outerMsg.substring(outerMsg.indexOf("{"))).getString("message")
                if (nestedMsg.contains("PSP response contains error: The amount of the \"Refund\" transaction exceeds the amount of the \"Debit\" (Purchase) transaction"))
                    callback(false, "Refund is under process")
                else
                    callback(false, "Failed to process refund: ${response.code()} - $nestedMsg")
            } catch (e: JSONException) {
                Log.e("API_ERROR", "Error parsing error message", e)
                callback(
                    false,
                    "Failed to process refund: ${response.code()} - ${response.message()}"
                )
            }
        }
    } catch (e: Exception) {
        Log.e("API_ERROR", "Error processing refund: ${e.message}")
        callback(false, "An Unexpected Error Occurred, Please Try Again Later")
    }
}

suspend fun fetchPaymentToken(
    amount: String,
    referenceId: String,
    merchantId: String,
    currency: String
): String? {
    return try {
        val paymentResponse = MerchantService.getPaymentToken(
            PaymentRequest(
                client_id = BuildConfig.SOLUTION_PARTNER_ID,
                client_secret = BuildConfig.AUTH_PASSWORD,
                UID = BuildConfig.MERCHANT_BANK_ID,
                login = merchantId,
                data = PaymentData(
                    language = "en",
                    edit = false,
                    receipt = true,
                    stay = false,
                    operation = "*********",
                    currency = currency,
                    amount = amount,
                    rrn = referenceId
                )
            )
        )
        val paymentToken = paymentResponse.data?.token
        paymentToken
    } catch (e: Exception) {
        e.printStackTrace()
        Log.e("API_ERROR", "Error fetching tokens: ${e.message}")
        null
    }
}

suspend fun loginUser(
    context: Context,
    email: String,
    password: String
): ApiResponse<LoginData>? {
    return try {
        val loginRequest = LoginRequest(email, password)
        val response = MerchantService.login(loginRequest)

        if (response.status) {
            response.data?.let { loginData: LoginData ->
                val expiryTime = getExpiryTimeFromToken(loginData.token)
                if (expiryTime != null) {
                    val userDataWithExpiry = loginData.copy(
                        user = loginData.user.copy(),
                        roles = loginData.roles,
                        type = loginData.type,
                        token = loginData.token,
                        tokenExpiry = expiryTime
                    )

                    val userDataJson = Gson().toJson(userDataWithExpiry)
                    context.saveToSharedPreferences("USER_DATA", userDataJson)
                }
            }
            response
        } else {
            Log.e("AuthHelper", "Login failed: ${response.message}")
            response
        }
    } catch (e: Exception) {
        e.printStackTrace()
        Log.e("AuthHelper", "Error during login: ${e.message}")
        null
    }
}

suspend fun fetchPaymentTerminalDetails(
    context: Context,
    userId: String
): ApiResponse<TerminalParentData>? {
    return try {
        val jwtToken = context.getUserData()?.token
        val response = MerchantService.getPaymentTerminalDetails("Bearer $jwtToken",userId)

        if (response.isSuccessful) {
            response.body()?.also { apiResponse ->
                if (apiResponse.status && apiResponse.data != null) {
                    val terminalData = apiResponse.data.paymentTerminal
                    val userData = apiResponse.data.user

                    val gson = Gson()
                    val terminalJson = gson.toJson(terminalData)
                    val userJson = gson.toJson(userData)

                    context.saveToSharedPreferences("USER_TERMINAL_DATA", terminalJson)
                    context.saveToSharedPreferences("USER_PARENT_DATA", userJson)

                    Log.d("API_SUCCESSFPTD", "Data saved successfully! ${apiResponse.data}")
                } else {
                    Log.e("API_ERROR", "Failed: ${apiResponse.message}")
                }
            }
        } else {
            val errorMessage = response.errorBody()?.string() ?: "Unknown error"
            Log.e("API_RESPONSE", "Error: $errorMessage")
            null
        }
    } catch (e: Exception) {
        Log.e("API_ERROR", "Exception: ${e.message}")
        null
    }
}

suspend fun fetchPedDeviceStatus(
    context: Context,
    merchantBankId: String,
    merchantIdType: String,
    merchantId: String,
    terminalIdType: String,
    terminalId: String
): ApiResponse<PedStatusResponse>? {
    return try {
        val authToken = fetchAuthToken(context)
        if (authToken.isNullOrEmpty()) {
            Log.e("AUTH_ERROR", "Auth token is missing")
            return null
        }

        val requestPayload = PedStatusRequest(
            merchant_bank_id = merchantBankId,
            merchant_id_type = merchantIdType,
            merchant_id = merchantId,
            terminal_id_type = terminalIdType,
            terminal_id = terminalId
        )

        val response = MerchantService.getPedStatus("Bearer $authToken", requestPayload)

        if (response.isSuccessful) {
            response.body()?.also { apiResponse ->
                if (apiResponse.status && apiResponse.data != null) {
                    val pedData = apiResponse.data

                    Log.d("API_SUCCESS_PED", "PED Status retrieved: $pedData")
                    val gson = Gson()
                    val pedJson = gson.toJson(pedData)
                    context.saveToSharedPreferences("USER_DEVICE_PED_STATUS", pedJson)

                } else {
                    Log.e("API_ERROR", "Failed: ${apiResponse.message}")
                }
            }
        } else {
            val errorMessage = response.errorBody()?.string() ?: "Unknown error"
            Log.e("API_RESPONSE", "Error: $errorMessage")
            null
        }
    } catch (e: Exception) {
        Log.e("API_ERROR", "Exception: ${e.message}")
        null
    }
}

suspend fun fetchAndStoreUserData(context: Context): Pair<Boolean, PedStatusResponse?> {
    return withContext(Dispatchers.IO) {
        val userData = context.getUserData()
        if (userData?.user?.id == null) {
            Log.e("FETCH_ERROR", "User ID is null")
            return@withContext Pair(false, null)
        }

        val userId = userData.user.id
        var terminalInfo = context.getUserTerminalData()

        if (terminalInfo == null) {
            val terminalDataResponse = fetchPaymentTerminalDetails(context, userId)

            if (terminalDataResponse == null) {
                Log.e("FETCH_ERROR", "Failed to fetch payment terminal details")
                return@withContext Pair(false, null)
            }

            Log.d("API_SUCCESS", "User and Terminal Data saved successfully!")
            terminalInfo = terminalDataResponse.data?.paymentTerminal // Update terminalInfo
        }

        if (terminalInfo?.terminalName == null) {
            Log.e("FETCH_ERROR", "User Terminal Data is null")
            return@withContext Pair(false, null)
        }

        val terminalName = terminalInfo.terminalName

        val pedDataResponse = fetchPedDeviceStatus(
            context = context,
            merchantBankId = BuildConfig.MERCHANT_BANK_ID,
            merchantIdType = "merchant_email",
            merchantId = context.getUserParentData()?.email!! ,
            terminalIdType = "merchant_terminal_id",
            terminalId = terminalName
        )

        if (pedDataResponse == null) {
            Log.e("FETCH_ERROR", "Failed to fetch PED Device Status")
            return@withContext Pair(false, null)
        }

        Log.d("API_SUCCESS_PED", "PED Status saved successfully!")
        return@withContext Pair(true, pedDataResponse.data)
    }
}

suspend fun unbindPaymentTerminal(
    context: Context,
    merchantBankId: String,
    merchantIdType: String,
    merchantId: String,
    terminalIdType: String,
    terminalId: String
): Boolean {
    return try {
        val authToken = fetchAuthToken(context)
        if (authToken.isNullOrEmpty()) {
            Log.e("AUTH_ERROR", "Auth token is missing")
            return false
        }

        val requestPayload = PedStatusRequest(
            merchant_bank_id = merchantBankId,
            merchant_id_type = merchantIdType,
            merchant_id = merchantId,
            terminal_id_type = terminalIdType,
            terminal_id = terminalId
        )

        val response = MerchantService.unbindDevice("Bearer $authToken", requestPayload)

        if (response.code() == 204) {
            Log.d("API_SUCCESS", "Device unbound successfully")
            true
        } else {
            val errorMessage = response.errorBody()?.string() ?: "Unknown error"
            Log.e("API_ERROR", "Failed to unbind device: $errorMessage")
            false
        }
    } catch (e: Exception) {
        Log.e("API_EXCEPTION", "Exception: ${e.message}")
        false
    }
}

suspend fun fetchBusinessDayStatus(
    context: Context,
    merchantBankId: String,
    merchantIdType: String,
    merchantId: String,
    terminalIdType: String,
    terminalId: String
): ApiResponse<BusinessDayResponse>? {
    return try {
        val authToken = fetchAuthToken(context)
        if (authToken.isNullOrEmpty()) {
            Log.e("AUTH_ERROR", "Auth token is missing")
            return null
        }

        val request = BusinessDayRequest(
            merchantBankId = merchantBankId,
            merchantIdType = merchantIdType,
            merchantId = merchantId,
            terminalIdType = terminalIdType,
            terminalId = terminalId
        )

        Log.e("API_SUCCESS", request.toString())

        val response = MerchantService.getBusinessDayStatus("Bearer $authToken", request)

        if (response.isSuccessful) {
            response.body()?.also { apiResponse ->
                Log.d("API_SUCCESS", "Business Day Status: ${apiResponse.data?.description}")
                return apiResponse
            }
        } else {
            val errorMessage = response.errorBody()?.string() ?: "Unknown error"
            Log.e("API_RESPONSE", "Error: $errorMessage")
            null
        }
    } catch (e: Exception) {
        Log.e("API_ERROR", "Exception: ${e.message}")
        null
    }
}

suspend fun closeBusinessDay(
    context: Context,
    merchantBankId: String,
    merchantIdType: String,
    merchantId: String,
    terminalIdType: String,
    terminalId: String
): Boolean {
    return try {
        val authToken = fetchAuthToken(context)
        if (authToken.isNullOrEmpty()) {
            Log.e("AUTH_ERROR", "Auth token is missing")
            return false
        }

        val request = BusinessDayRequest(
            merchantBankId = merchantBankId,
            merchantIdType = merchantIdType,
            merchantId = merchantId,
            terminalIdType = terminalIdType,
            terminalId = terminalId
        )

        val response = MerchantService.closeBusinessDay("Bearer $authToken", request)

        if (response.isSuccessful) {
            Log.d("API_SUCCESS", "Business day closed successfully")
            return true
        } else if (response.code() == 404) {
            Log.e("API_RESPONSE", "Business day not found")
        } else {
            val errorMessage = response.errorBody()?.string() ?: "Unknown error"
            Log.e("API_RESPONSE", "Error: $errorMessage")
        }

        false
    } catch (e: Exception) {
        Log.e("API_ERROR", "Exception: ${e.message}")
        false
    }
}

suspend fun sendOnboardingRequest(
    email: String,
    merchantType: String,
    country: String,
    companyName: String,
    phone: String,
    expectedTransactionsPerMonth: String,
    expectedMonthlyActiveTerminals: String
): Pair<Boolean, String> {
    return try {
        val request = OnboardingRequest(
            email = email,
            merchantType = merchantType,
            country = country,
            companyName = companyName,
            phone = phone,
            expectedTransactionsPerMonth = expectedTransactionsPerMonth,
            expectedMonthlyActiveTerminals = expectedMonthlyActiveTerminals
        )

        val response = MerchantService.sendOnboardingRequest(request)

        if (response.isSuccessful) {
            Log.d("API_SUCCESS", "Onboarding request saved successfully")
            Pair(true, "Onboarding request saved successfully")
        } else {
            val errorMessage = response.errorBody()?.string() ?: "Unknown error occurred"
            Log.e("API_RESPONSE", "Error: $errorMessage")
            Pair(false, errorMessage)
        }
    } catch (e: Exception) {
        Log.e("API_ERROR", "Exception: ${e.message}")
        Pair(false, e.message ?: "An unexpected error occurred")
    }
}

suspend fun createSupportIssue(
    context: Context,
    summary: String,
    text: String
): Pair<Boolean, String> {
    val issueRequest = SupportRequest.create(summary, text)

    val userData = context.getUserData()
    val token = userData?.token ?: ""

    return try {
        val response = withContext(Dispatchers.IO) {
            MerchantService.createIssue("Bearer $token", issueRequest)
        }

        val result: Pair<Boolean, String> = if (response.isSuccessful) {
            val responseBody = response.body()
            if (responseBody != null && responseBody.status) {
                Pair(true, responseBody.message)
            } else {
                Log.e("Support Issue", "Failed to create issue: ${responseBody?.message}")
                Pair(false, "Onboarding request saved successfully")
            }
        } else {
            val errorMessage = response.errorBody()?.string() ?: "Unknown error occurred"
            Log.e("Support Issue", "API Error: $errorMessage")
            Pair(false, errorMessage)
        }

        result

    } catch (e: Exception) {
        Log.e("Support Issue", "Exception: ${e.message}")
        Pair(false, e.message ?: "An unexpected error occurred")
    }
}

suspend fun sendReceiptEmail(context: Context, email: String, dataMap: Map<String, String>, VAT: String?, tipPercentage: String?, tipAmount: String?, parentId: String): Boolean {
    val localDateTime = if (dataMap["date_time"] != null) context.convertToUserTimeZone(dataMap["date_time"]!!) else null

    val paymentReceipt = PaymentReceipt(
        status = dataMap["status"] ?: "",
        bank_owner = dataMap["bank_owner"] ?: "",
        trxid = dataMap["trxid"] ?: "",
        pmt_name = dataMap["pmt_name"] ?: "",
        pmt_dest = dataMap["pmt_dest"] ?: "",
        unp = dataMap["unp"],
        aid = dataMap["aid"] ?: "",
        applbl = dataMap["applbl"] ?: "",
        card_mask = dataMap["card_mask"] ?: "",
        rrn = dataMap["rrn"] ?: "",
        auth_code = dataMap["auth_code"] ?: "",
        pmt_terminal = dataMap["pmt_terminal"],
        terminal = dataMap["terminal"],
        amt = dataMap["amt"] ?: "",
        cur_code = dataMap["cur_code"] ?: "",
        date_time = localDateTime ?: dataMap["date_time"] ?: "",
        tvr = dataMap["tvr"],
        host_resp_code = dataMap["host_resp_code"]
    )

    val vatValue = if (VAT.isNullOrEmpty() || VAT == context.getString(R.string.vat_off)) null else VAT
    val tipPercentageValue = if (tipPercentage.isNullOrEmpty() || tipPercentage == "0%") null else tipPercentage
    val tipAmountValue = if (tipAmount.isNullOrEmpty() || tipAmount == "0.0") null else tipAmount

    val requestBody = TransactionReceiptRequest(
        paymentReceipt = paymentReceipt,
        receiverEmail = email,
        merchantId = parentId,
        VAT = vatValue,
        tipPercentage = tipPercentageValue,
        tipAmount = tipAmountValue
    )

    val jwtToken = context.getUserData()?.token

    return try {
        val response = withContext(Dispatchers.IO) {
            MerchantService.sendTransactionReceipt("Bearer $jwtToken" ,requestBody)
        }

        if (response.isSuccessful && response.body()?.status == true) {
            return response.body()?.status!!
        } else {
            Log.e("API_ERROR", "Failed: ${response.errorBody()?.string()}")
            return response.body()?.status!!
        }
    } catch (e: Exception) {
        // Handle exception
        Log.e("API_EXCEPTION", "Exception: ${e.localizedMessage}")
        false
    }
}

suspend fun updateUserName(context: Context, firstName: String, lastName: String): Boolean {
    return try {
        val token = context.getUserData()?.token ?: ""

        val request = UpdateUserRequest(firstName, lastName)

        val response = MerchantService.updateUserName(
            request,
            "Bearer $token"
        )

        if (response.isSuccessful) {
            var userData = context.getUserData()
            val gson = Gson()

            userData?.user?.firstName = firstName
            userData?.user?.lastName = lastName

            context.saveToSharedPreferences("USER_DATA",gson.toJson(userData))
            true
        }

        else {
            false
        }
    } catch (e: Exception) {
        e.printStackTrace()
        false
    }
}

suspend fun getActiveCredentials(context: Context): SecurityCredentialData? {
    val jwtToken = context.getUserData()?.token ?: return null
    val userParentId = context.getUserParentData()?.id ?: return null

    return try {
        val response = MerchantService.getActiveCredentials("Bearer $jwtToken", userParentId)
        if (response.status) {
            val gson = Gson()
            val activeCreds = gson.toJson(response.data)
            context.saveToSharedPreferences("USER_PARENT_SECURITY_CREDENTIAL_DATA", activeCreds)
            response.data
        } else {
            Log.e("ApiHelper", "Failed to fetch credentials: ${response.message}")
            null
        }
    } catch (e: Exception) {
        Log.e("ApiHelper", "Error fetching credentials", e)
        null
    }
}

suspend fun createQRPayment(
    context: Context,
    amount: String,
    referenceId: String,
    currency: String
): String? {
    val amountWithoutDecimal = amount.replace(".", "").toInt()
    val payload = EcommerceRequest(
        amount = amountWithoutDecimal,
        currencyCode = currency,
        locale = "en_GB",
        returnUrl = "https://hazelsone.com/return",
        merchantReferenceId = "Ecommerce $referenceId"
    )

    return try {
        val response = MerchantService.createQRCodePayment(context.getUserParentSecurityCredentialData()?.apiKey!!, context.getUserParentSecurityCredentialData()?.secretKey!!, true, payload)
        if (response.isSuccessful && response.body()?.status == true) {
            response.body()!!.data
        } else {
            Log.e("ApiHelper", "Payment creation failed: ${response.body()?.message}")
            null
        }
    } catch (e: Exception) {
        Log.e("ApiHelper", "Error creating payment", e)
        null
    }
}

suspend fun getTransactionStatus(transactionId: String): String? {
    return try {
        val response = MerchantService.getTransactionStatus(transactionId)
        if (response.status) {
            response.data?.status
        } else {
            Log.e("ApiHelper", "Failed to fetch transaction status: ${response.message}")
            null
        }
    } catch (e: Exception) {
        Log.e("ApiHelper", "Error fetching transaction status", e)
        null
    }
}

suspend fun getQrCodeDetails(context: Context, qrCodeId: String): ApiResponse<QrCodeDetails>? {
    return try {
        val token = context.getUserData()?.token ?: return null
        val response = MerchantService.getQrCodeDetails("Bearer $token", qrCodeId)
        response
    } catch (e: Exception) {
        Log.e("ApiHelper", "Error fetching QR code details", e)
        null
    }
}

suspend fun updateQrCodeStatus(context: Context, qrCodeId: String, status: String): Boolean {
    return try {
        val token = context.getUserData()?.token ?: return false
        val response = MerchantService.updateQrCodeStatus("Bearer $token", qrCodeId, status)

        if (response.status) {
            Log.d("ApiHelper", "QR code status updated successfully: ${response.message}")
            true
        } else {
            Log.e("ApiHelper", "Failed to update QR code status: ${response.message}")
            false
        }
    } catch (e: Exception) {
        Log.e("ApiHelper", "Error updating QR code status", e)
        false
    }
}


suspend fun fetchEcommerceTransactionData(
    context: Context,
    page: Int,
    userId: String,
    startDate: String? = null,
    endDate: String? = null,
    onResult: (EcommerceTransactionList?) -> Unit
) {
    try {
        val token = context.getUserData()?.token ?: return
        val response = withContext(Dispatchers.IO) {
            MerchantService.getEcommerceTransactions(
                token = "Bearer $token",
                userId = userId,
                page = page,
                startDate = startDate,
                endDate = endDate
            )
        }

        if (response.status) {
            onResult(response.data)
        } else {
            onResult(null)
        }
    } catch (_: Exception) {
        onResult(null)
    }
}

suspend fun getStaticQrCodes(context: Context, parentUserId: String): List<StaticQrCode>? {
    return try {
        val token = context.getUserData()?.token ?: return null
        val response = MerchantService.getStaticQrCodes("Bearer $token", parentUserId)

        if (response.status) {
            response.data
        } else {
            Log.e("ApiHelper", "Failed to fetch static QR codes: ${response.message}")
            null
        }
    } catch (e: Exception) {
        Log.e("ApiHelper", "Error fetching static QR codes", e)
        null
    }
}

suspend fun createStaticQrCode(context: Context, name: String, amount: Double, parentUserId: String): StaticQrCode? {
    return try {
        val token = context.getUserData()?.token ?: return null
        val request = StaticQrCodeRequest(
            name = name,
            defaultAmount = amount,
            parentUserId = parentUserId
        )

        val response = MerchantService.createStaticQrCode("Bearer $token", request)

        if (response.status) {
            Log.d("ApiHelper", "Static QR code created successfully: ${response.message}")
            response.data
        } else {
            Log.e("ApiHelper", "Failed to create static QR code: ${response.message}")
            null
        }
    } catch (e: Exception) {
        Log.e("ApiHelper", "Error creating static QR code", e)
        null
    }
}
