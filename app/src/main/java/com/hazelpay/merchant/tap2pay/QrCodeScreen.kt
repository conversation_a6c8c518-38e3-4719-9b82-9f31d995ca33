package com.hazelpay.merchant.tap2pay

import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.util.Log
import android.view.View
import android.view.WindowManager
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.hazelpay.merchant.tap2pay.utils.QrCodeStatus
import com.hazelpay.merchant.tap2pay.utils.SUCCESS_STATUSES
import com.hazelpay.merchant.tap2pay.utils.TransactionStatus
import com.hazelpay.merchant.tap2pay.utils.enforceOrientation
import com.hazelpay.merchant.tap2pay.utils.generateQrCode
import com.hazelpay.merchant.tap2pay.utils.getQrCodeDetails
import com.hazelpay.merchant.tap2pay.utils.getTransactionStatus
import com.hazelpay.merchant.tap2pay.utils.updateQrCodeStatus
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class QrCodeScreen : AppCompatActivity() {
    private lateinit var qrCodeImage: ImageView
    private lateinit var backButton: ImageButton
    private lateinit var progressOverlay: View
    private lateinit var loadingDotsText: TextView
    private var pollingJob: Job? = null
    private var transactionPollingJob: Job? = null
    private var loadingAnimationJob: Job? = null

    companion object {
        private const val QR_POLL_INTERVAL = 3000L // 3 seconds
        private const val QR_MAX_TIME = 2 * 60 * 1000L // 2 minutes
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_qr_code_screen)
        enforceOrientation(this)

        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

        qrCodeImage = findViewById(R.id.qr_code_image)
        backButton = findViewById(R.id.back_button)
        progressOverlay = findViewById(R.id.progress_overlay)
        loadingDotsText = findViewById(R.id.loading_dots)

        val qrCodeId = intent.getStringExtra("PAYMENT_ID")
        if (qrCodeId.isNullOrEmpty()) {
            finish()
            return
        }

        val qrCodeBitmap = generateQrCode(qrCodeId)
        qrCodeImage.setImageBitmap(qrCodeBitmap)

        backButton.setOnClickListener {
            pollingJob?.cancel()
            transactionPollingJob?.cancel()
            finish()
        }

        pollingJob = lifecycleScope.launch {
            pollQrCodeStatus(qrCodeId)
        }
    }

    private suspend fun pollQrCodeStatus(qrCodeId: String) {
        var elapsedTime = 0L

        while (elapsedTime < QR_MAX_TIME) {
            val response = getQrCodeDetails(this, qrCodeId)

            if (response == null) {
                delay(QR_POLL_INTERVAL)
                elapsedTime += QR_POLL_INTERVAL
                continue
            }

            if (!response.status || response.data == null) {
                delay(QR_POLL_INTERVAL)
                elapsedTime += QR_POLL_INTERVAL
                continue
            }

            val qrCodeDetails = response.data
            when (qrCodeDetails.status) {
                QrCodeStatus.CREATED.value -> {
                    Log.d("QrCodeStatus", "QR code still CREATED, continuing to poll")
                }
                QrCodeStatus.SCANNED.value -> {
                    runOnUiThread {
                        progressOverlay.visibility = View.VISIBLE
                        startLoadingAnimation()
                    }

                    val transactionId = qrCodeDetails.transactionId
                    if (!transactionId.isNullOrEmpty()) {
                        transactionPollingJob = lifecycleScope.launch {
                            pollTransactionStatus(transactionId)
                        }
                        return
                    } else {
                        showFailureScreen()
                        return
                    }
                }
                QrCodeStatus.FAILED.value -> {
                    showFailureScreen()
                    return
                }
                else -> {
                    Log.e("QrCodeStatus", "Unknown QR code status: ${qrCodeDetails.status}")
                }
            }

            delay(QR_POLL_INTERVAL)
            elapsedTime += QR_POLL_INTERVAL
        }

        val updated = updateQrCodeStatus(this, qrCodeId, QrCodeStatus.FAILED.value)
        if (updated) {
            Log.d("QrCodeStatus", "QR code status updated to FAILED successfully")
        } else {
            Log.e("QrCodeStatus", "Failed to update QR code status to FAILED")
        }

        showFailureScreen()
    }

    private suspend fun pollTransactionStatus(transactionId: String) {
        val maxTime = 5 * 60 * 1000L // 5 minutes
        val pollInterval = 3000L // 3 seconds
        var elapsedTime = 0L

        while (elapsedTime < maxTime) {
            val transactionData = getTransactionStatus(transactionId)

            when (transactionData) {
                in SUCCESS_STATUSES -> {
                    showSuccessScreen()
                    return
                }
                TransactionStatus.CREATED.value -> {
                    Log.d("TransactionStatus", "Transaction still created, continuing to poll")
                }
                TransactionStatus.IN_PROGRESS.value -> {
                    Log.d("TransactionStatus", "Transaction in progress, continuing to poll")
                }
                null -> {
                    Log.e("TransactionStatus", "Failed to fetch status")
                }
                else -> {
                    showFailureScreen()
                    return
                }
            }

            delay(pollInterval)
            elapsedTime += pollInterval
        }

        showFailureScreen()
    }

    private fun showSuccessScreen() {
        finish()
        val intent = Intent(this@QrCodeScreen, TransferSuccessfulScreen::class.java).apply {
            putExtra("SHOW_SINGLE_BUTTON", true)
        }
        startActivity(intent)
    }

    private fun showFailureScreen() {
        finish()
        startActivity(Intent(this@QrCodeScreen, TransferFailedScreen::class.java))
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        enforceOrientation(this)
    }

    private fun startLoadingAnimation() {
        loadingAnimationJob = lifecycleScope.launch {
            val dots = arrayOf(".  ", ".. ", "...")
            var index = 0
            while (true) {
                runOnUiThread {
                    loadingDotsText.text = dots[index]
                }
                index = (index + 1) % dots.size
                delay(500)
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        pollingJob?.cancel()
        transactionPollingJob?.cancel()
        loadingAnimationJob?.cancel()
    }
}
