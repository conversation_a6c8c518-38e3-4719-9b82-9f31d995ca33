package com.hazelpay.merchant.tap2pay.utils

import android.app.Activity
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo
import android.content.res.Resources
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.graphics.RectF
import android.provider.Settings
import android.widget.Toast
import androidx.biometric.BiometricManager
import androidx.biometric.BiometricPrompt
import androidx.core.content.ContextCompat
import com.hazelpay.merchant.tap2pay.LoginScreen
import com.hazelpay.merchant.tap2pay.model.LoginData
import com.hazelpay.merchant.tap2pay.model.PaymentTerminal
import com.hazelpay.merchant.tap2pay.model.UserData
import com.google.android.play.core.appupdate.AppUpdateManager
import com.google.android.play.core.appupdate.AppUpdateManagerFactory
import com.google.android.play.core.install.model.AppUpdateType
import com.google.android.play.core.install.model.UpdateAvailability
import com.google.gson.Gson
import com.hazelpay.merchant.tap2pay.R
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import kotlin.math.sqrt
import androidx.core.net.toUri
import androidx.core.content.edit
import com.google.zxing.BarcodeFormat
import com.google.zxing.MultiFormatWriter
import com.google.zxing.common.BitMatrix
import com.hazelpay.merchant.tap2pay.model.SecurityCredentialData
import androidx.core.graphics.createBitmap
import androidx.core.graphics.set
import com.hazelpay.merchant.tap2pay.BuildConfig

fun Context.saveToSharedPreferences(key: String, value: String) {
    val sharedPreferences = getSharedPreferences("EftaaPayPrefs", Context.MODE_PRIVATE)
    sharedPreferences.edit().apply {
        putString(key, value)
        apply()
    }
}

fun Context.getFromSharedPreferences(key: String): String? {
    val sharedPreferences = getSharedPreferences("EftaaPayPrefs", Context.MODE_PRIVATE)
    return sharedPreferences.getString(key, null)
}

fun Context.clearFromSharedPreferences(key: String) {
    val sharedPreferences = getSharedPreferences("EftaaPayPrefs", Context.MODE_PRIVATE)
    sharedPreferences.edit {
        remove(key)
    }
}

fun Context.clearAllSharedPreferences() {
    val sharedPreferences = getSharedPreferences("EftaaPayPrefs", Context.MODE_PRIVATE)
    sharedPreferences.edit {
        clear()
    }
}

fun Context.getUserData(): LoginData? {
    val userDataJson = getFromSharedPreferences("USER_DATA")
    return if (userDataJson != null) {
        Gson().fromJson(userDataJson, LoginData::class.java)
    } else {
        null
    }
}

fun Context.getUserTerminalData(): PaymentTerminal? {
    val userTerminalDataJson = getFromSharedPreferences("USER_TERMINAL_DATA")
    return if (userTerminalDataJson != null) {
        Gson().fromJson(userTerminalDataJson, PaymentTerminal::class.java)
    } else {
        null
    }
}

fun Context.getUserParentData(): UserData? {
    val userParentDataJson = getFromSharedPreferences("USER_PARENT_DATA")
    return if (userParentDataJson != null) {
        Gson().fromJson(userParentDataJson, UserData::class.java)
    } else {
        null
    }
}

fun getExpiryTimeFromToken(token: String?): Long? {
    return try {
        if (token == null) return null
        val parts = token.split(".")
        if (parts.size != 3) return null

        val payload = String(android.util.Base64.decode(parts[1], android.util.Base64.URL_SAFE or android.util.Base64.NO_PADDING))
        val json = org.json.JSONObject(payload)
        json.optLong("exp") * 1000
    } catch (e: Exception) {
        e.printStackTrace()
        null
    }
}

fun isSessionValid(context: Context): Boolean {
    val userData = context.getUserData()
    val tokenExpiry = userData?.tokenExpiry ?: 0
    return System.currentTimeMillis() < tokenExpiry
}

fun handleSessionExpiry(context: Context) {
    context.clearAllSharedPreferences()
    Toast.makeText(context, context.getString(R.string.session_expired_message), Toast.LENGTH_SHORT).show()

    val intent = Intent(context, LoginScreen::class.java).apply {
        flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
    }
    context.startActivity(intent)
}

fun isBiometricAvailable(context: Context): Boolean {
    val biometricManager = BiometricManager.from(context)
    return when (biometricManager.canAuthenticate(BiometricManager.Authenticators.BIOMETRIC_STRONG)) {
        BiometricManager.BIOMETRIC_SUCCESS -> true
        BiometricManager.BIOMETRIC_ERROR_NONE_ENROLLED -> {
            false
        }
        BiometricManager.BIOMETRIC_ERROR_NO_HARDWARE -> {
            false
        }
        BiometricManager.BIOMETRIC_ERROR_HW_UNAVAILABLE -> {
            false
        }
        else -> false
    }
}

fun showBiometricPrompt(
    context: Context,
    onSuccess: () -> Unit,
    onError: (String) -> Unit
) {
    val executor = ContextCompat.getMainExecutor(context)
    val biometricPrompt = BiometricPrompt(
        context as androidx.fragment.app.FragmentActivity,
        executor,
        object : BiometricPrompt.AuthenticationCallback() {
            override fun onAuthenticationSucceeded(result: BiometricPrompt.AuthenticationResult) {
                super.onAuthenticationSucceeded(result)
                onSuccess()
            }

            override fun onAuthenticationError(errorCode: Int, errString: CharSequence) {
                super.onAuthenticationError(errorCode, errString)
                onError(errString.toString())
            }

            override fun onAuthenticationFailed() {
                super.onAuthenticationFailed()
                onError("Authentication failed. Please try again.")
            }
        }
    )

    val promptInfo = BiometricPrompt.PromptInfo.Builder()
        .setTitle("EftaaPay Biometric Authentication")
        .setSubtitle("Use your fingerprint or face to authenticate")
        .setDescription("For secure access, please use biometric authentication.")
        .setNegativeButtonText("Cancel")
        .setAllowedAuthenticators(
            BiometricManager.Authenticators.BIOMETRIC_STRONG or
                    BiometricManager.Authenticators.BIOMETRIC_WEAK
        )
        .build()

    biometricPrompt.authenticate(promptInfo)
}


fun Context.setBiometricEnabled(enabled: Boolean) {
    saveToSharedPreferences("BIOMETRIC_ENABLED", enabled.toString())
}

fun Context.isBiometricEnabled(): Boolean {
    return getFromSharedPreferences("BIOMETRIC_ENABLED")?.toBoolean() == true
}

fun authenticateWithBiometrics(context: Context, onSuccess: () -> Unit, onFailure: (String) -> Unit) {
    if (context.isBiometricEnabled()) {
        showBiometricPrompt(
            context = context,
            onSuccess = {
                val userData = context.getUserData()
                val sessionToken = userData?.token

                if (sessionToken != null && isSessionValid(context)) {
                    onSuccess()
                } else {
                    onFailure("Session token not found or session expired.")
                }
            },
            onError = { errorMessage ->
                onFailure(errorMessage)
            }
        )
    } else {
        onFailure("Biometric authentication is not enabled.")
    }
}

fun enforceOrientation(activity: Activity) {
    val metrics = Resources.getSystem().displayMetrics
    val widthInches = metrics.widthPixels / metrics.xdpi
    val heightInches = metrics.heightPixels / metrics.ydpi
    val diagonalInches = sqrt(widthInches * widthInches + heightInches * heightInches)
    val isTablet = diagonalInches >= 7.0

    if (isTablet) {
        activity.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED // Allow both orientations
    } else {
        activity.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED // Lock to portrait
    }
}

fun copyDeviceIdToClipboard(context: Context) {
    val deviceId = Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID)

    if (deviceId.isNullOrEmpty()) {
        Toast.makeText(context, "Failed to retrieve device ID", Toast.LENGTH_LONG).show()
        return
    }

    val clipboard = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
    val clip = ClipData.newPlainText("Device ID", deviceId)
    clipboard.setPrimaryClip(clip)
}

fun Context.convertToUserTimeZone(transactionDateTime: String): String? {
    return try {
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")

        val cetDateTime = LocalDateTime.parse(transactionDateTime, formatter)

        val cetZonedTime = cetDateTime.atZone(ZoneId.of("CET"))

        val userZonedTime = cetZonedTime.withZoneSameInstant(ZoneId.systemDefault())

        userZonedTime.format(formatter)
    } catch (e: Exception) {
        e.printStackTrace()
        null
    }
}

fun checkAndUpdateApp(activity: Activity, onUpdateFailed: (Boolean) -> Unit) {
    val appUpdateManager: AppUpdateManager = AppUpdateManagerFactory.create(activity)

    val appUpdateInfoTask = appUpdateManager.appUpdateInfo

    appUpdateInfoTask.addOnSuccessListener { appUpdateInfo ->
        if (appUpdateInfo.updateAvailability() == UpdateAvailability.UPDATE_AVAILABLE &&
            appUpdateInfo.isUpdateTypeAllowed(AppUpdateType.IMMEDIATE)
        ) {
            try {
                appUpdateManager.startUpdateFlowForResult(
                    appUpdateInfo,
                    AppUpdateType.IMMEDIATE,
                    activity,
                    1001
                )
                onUpdateFailed(true)
            } catch (e: Exception) {
                e.printStackTrace()
                onUpdateFailed(false)
            }
        }
    }.addOnFailureListener {
        onUpdateFailed(false)
    }
}

fun openPlayStore(context: Context, packageName: String = context.packageName) {
    try {
        val intent = Intent(Intent.ACTION_VIEW, "market://details?id=$packageName".toUri())
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        context.startActivity(intent)
    } catch (_: Exception) {
        // If Play Store app is not installed, open in browser
        try {
            val intent = Intent(Intent.ACTION_VIEW, "https://play.google.com/store/apps/details?id=$packageName".toUri())
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            context.startActivity(intent)
        } catch (_: Exception) {
            Toast.makeText(context, "Unable to open Play Store", Toast.LENGTH_SHORT).show()
        }
    }
}

fun Context.getUserParentSecurityCredentialData(): SecurityCredentialData? {
    val userParentSCDataJson = getFromSharedPreferences("USER_PARENT_SECURITY_CREDENTIAL_DATA")
    return if (userParentSCDataJson != null) {
        Gson().fromJson(userParentSCDataJson, SecurityCredentialData::class.java)
    } else {
        null
    }
}

fun generateQrCode(paymentId: String): Bitmap {
    val width = 300
    val height = 300

    val envPath = if (BuildConfig.FLAVOR.contains("preprod", ignoreCase = true)) {
        "/preprod"
    } else {
        "/prod"
    }

    // Create a deep link URL that will work with app links
    val deepLinkUrl = "https://hazelsone.com$envPath/qrcode?id=$paymentId"

    val bitMatrix: BitMatrix = MultiFormatWriter().encode(
        deepLinkUrl,
        BarcodeFormat.QR_CODE,
        width,
        height
    )

    val bitmap = createBitmap(width, height, Bitmap.Config.RGB_565)
    for (x in 0 until width) {
        for (y in 0 until height) {
            bitmap[x, y] = if (bitMatrix[x, y]) Color.BLACK else Color.WHITE
        }
    }

    val output = createBitmap(width, height)
    val canvas = Canvas(output)
    val paint = Paint().apply { isAntiAlias = true }
    val rectF = RectF(0f, 0f, width.toFloat(), height.toFloat())
    val radius = 20f

    canvas.drawRoundRect(rectF, radius, radius, paint)
    paint.xfermode = PorterDuffXfermode(PorterDuff.Mode.SRC_IN)
    canvas.drawBitmap(bitmap, 0f, 0f, paint)

    return output
}

fun generateStaticQrCode(staticQrCodeId: String): Bitmap {
    val width = 300
    val height = 300

    val envPath = if (BuildConfig.FLAVOR.contains("preprod", ignoreCase = true)) {
        "/preprod"
    } else {
        "/prod"
    }

    val deepLinkUrl = "https://hazelsone.com$envPath/pay/static?id=$staticQrCodeId"

    val bitMatrix: BitMatrix = MultiFormatWriter().encode(
        deepLinkUrl,
        BarcodeFormat.QR_CODE,
        width,
        height
    )

    val bitmap = createBitmap(width, height, Bitmap.Config.RGB_565)
    for (x in 0 until width) {
        for (y in 0 until height) {
            bitmap[x, y] = if (bitMatrix[x, y]) Color.BLACK else Color.WHITE
        }
    }

    val output = createBitmap(width, height)
    val canvas = Canvas(output)
    val paint = Paint().apply { isAntiAlias = true }
    val rectF = RectF(0f, 0f, width.toFloat(), height.toFloat())
    val radius = 20f

    canvas.drawRoundRect(rectF, radius, radius, paint)
    paint.xfermode = PorterDuffXfermode(PorterDuff.Mode.SRC_IN)
    canvas.drawBitmap(bitmap, 0f, 0f, paint)

    return output
}

fun getCurrencySymbolById(currencyId: Int): String {
    return CurrencyConstants.CURRENCY_SYMBOLS.find { it.first == currencyId }?.second ?: ""
}

