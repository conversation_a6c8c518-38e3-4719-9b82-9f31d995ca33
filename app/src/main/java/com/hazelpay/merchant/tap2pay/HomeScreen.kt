package com.hazelpay.merchant.tap2pay

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.provider.Settings
import android.util.Log
import android.view.View
import android.view.WindowManager
import android.widget.*
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.content.res.AppCompatResources
import androidx.cardview.widget.CardView
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.google.android.material.navigation.NavigationBarView
import com.google.android.material.tabs.TabLayout
import com.hazelpay.merchant.tap2pay.model.*
import com.hazelpay.merchant.tap2pay.adapter.*
import com.hazelpay.merchant.tap2pay.utils.*
import com.hazelpay.merchant.tap2pay.utils.MerchantType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale

class HomeScreen : AppCompatActivity() {

    private companion object {
        private const val MERCHANT_BANK_ID = BuildConfig.MERCHANT_BANK_ID
        private const val SOLUTION_PARTNER_ID = BuildConfig.SOLUTION_PARTNER_ID
        private const val APP_LANGUAGE_CODE = "en"
    }

    private var currentPage = 1
    private var isLoading = false
    private var hasMoreData = true
    private var currentTab = TransactionTab.CARD_PRESENT
    private lateinit var merchantType: MerchantType

    private lateinit var cardPresentAdapter: TransactionAdapter
    private lateinit var ecommerceAdapter: EcommerceTransactionAdapter
    private val cardPresentTransactions = mutableListOf<TransactionItem.Transaction>()
    private val ecommerceTransactions = mutableListOf<EcommerceTransactionItem>()
    private lateinit var tabLayout: TabLayout

    private lateinit var closeButton: Button
    private lateinit var swipeRefreshLayout: SwipeRefreshLayout
    private lateinit var bottomNavigationView: BottomNavigationView
    private lateinit var connectivityChecker: ConnectivityChecker

    private enum class TransactionTab {
        CARD_PRESENT, ECOMMERCE
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_home_screen)
        enforceOrientation(this)

        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);

        swipeRefreshLayout = findViewById(R.id.swipeRefreshLayout)
        bottomNavigationView = findViewById(R.id.bottom_navigation)
        tabLayout = findViewById(R.id.transaction_tab_layout)
        connectivityChecker = ConnectivityChecker(this)

        merchantType = MerchantType.fromString(getUserParentData()?.merchantType)

        setupRecyclerView()
        setupBottomNavigation()
        setupCloseButton()
        setupTabLayout()
        showLoader()

        findViewById<TextView>(R.id.txt_username_home).text =
            if (getUserData()?.user?.firstName.isNullOrEmpty() || getUserData()?.user?.lastName.isNullOrEmpty())
                "Welcome to Merchant App "
            else "Hi ${getUserData()?.user?.firstName} ${getUserData()?.user?.lastName} 👋"

        swipeRefreshLayout.setColorSchemeResources(R.color.lavender)

        swipeRefreshLayout.setOnRefreshListener {
            lifecycleScope.launch {
                refreshContent()
            }
        }
    }

    private fun setupTabLayout() {
        when (merchantType) {
            MerchantType.ONLINE -> {
                tabLayout.addTab(tabLayout.newTab().setText("Ecommerce"))
                currentTab = TransactionTab.ECOMMERCE
            }
            MerchantType.CARDPRESENT -> {
                tabLayout.addTab(tabLayout.newTab().setText("Card Present"))
                currentTab = TransactionTab.CARD_PRESENT
            }
            MerchantType.GROUP -> {
                tabLayout.addTab(tabLayout.newTab().setText("Card Present"))
                tabLayout.addTab(tabLayout.newTab().setText("Ecommerce"))
            }
        }

        if (tabLayout.tabCount <= 1) {
            tabLayout.visibility = View.GONE
        } else {
            tabLayout.visibility = View.VISIBLE
        }

        tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                showLoader()
                val newTab = when (merchantType) {
                    MerchantType.ONLINE -> TransactionTab.ECOMMERCE
                    MerchantType.CARDPRESENT -> TransactionTab.CARD_PRESENT
                    MerchantType.GROUP -> {
                        when (tab?.position) {
                            0 -> TransactionTab.CARD_PRESENT
                            1 -> TransactionTab.ECOMMERCE
                            else -> TransactionTab.CARD_PRESENT
                        }
                    }
                }

                currentTab = newTab
                resetAndFetchTransactions()
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {}
            override fun onTabReselected(tab: TabLayout.Tab?) {
                showLoader()
                resetAndFetchTransactions()
            }
        })
    }

    override fun onStart() {
        super.onStart()
        connectivityChecker.startListening()
    }

    override fun onStop() {
        super.onStop()
        connectivityChecker.stopListening()
    }

    private suspend fun refreshContent() {
        showLoader()
        getAndStoreUserPEDDetails(this@HomeScreen)
        checkAndDisplayBusinessDayStatus(this@HomeScreen)
        withContext(Dispatchers.Main) {
            refreshTransactionList()
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        enforceOrientation(this)
    }

    override fun onResume() {
        super.onResume()
        checkAndUpdateApp(this) { success ->
            if (!success) {
                openPlayStore(this)
            }
        }
        lifecycleScope.launch {
            refreshContent()
        }
    }

    private fun setupCloseButton() {
        closeButton = findViewById(R.id.sdk_status_close_button)
        closeButton.setOnClickListener {
            val sdkStatusOverlay = findViewById<FrameLayout>(R.id.sdk_status_overlay)
            sdkStatusOverlay.visibility = View.GONE
        }
    }

    private fun setupRecyclerView() {
        val recyclerView: RecyclerView = findViewById(R.id.hs_transaction_recycler_view)

        cardPresentAdapter = TransactionAdapter(
            transactions = cardPresentTransactions,
            context = this,
            lifecycleOwner = this
        ) {
            resetAndFetchTransactions()
        }

        ecommerceAdapter = EcommerceTransactionAdapter(
            transactions = ecommerceTransactions,
            context = this
        )

        recyclerView.layoutManager = LinearLayoutManager(this)
        recyclerView.adapter = cardPresentAdapter

        recyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(rv: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(rv, dx, dy)
                val layoutManager = rv.layoutManager as LinearLayoutManager
                val totalItemCount = layoutManager.itemCount
                val lastVisibleItemPosition = layoutManager.findLastVisibleItemPosition()

                if (!isLoading && hasMoreData && lastVisibleItemPosition + 5 >= totalItemCount) {
                    currentPage++
                    lifecycleScope.launch {
                        fetchTransactionData()
                    }
                }
            }
        })
    }

    private fun setupBottomNavigation() {
        val bottomNavigationView: BottomNavigationView = findViewById(R.id.bottom_navigation)
        bottomNavigationView.labelVisibilityMode = NavigationBarView.LABEL_VISIBILITY_SELECTED
        bottomNavigationView.selectedItemId = R.id.home

        bottomNavigationView.setOnItemSelectedListener { item ->
            when (item.itemId) {
                R.id.home -> true
                R.id.sales -> {
                    startActivity(Intent(this, PaymentScreen::class.java))
                    finish()
                    true
                }

                R.id.reports -> {
                    startActivity(Intent(this, ReportsScreen::class.java))
                    finish()
                    true
                }

                R.id.settings -> {
                    startActivity(Intent(this, SettingsScreen::class.java))
                    finish()
                    true
                }

                else -> false
            }
        }

        when (merchantType) {
            MerchantType.ONLINE, MerchantType.GROUP -> {
                bottomNavigationView.menu.findItem(R.id.sales).isEnabled = true
            }
            MerchantType.CARDPRESENT -> {
                val isOpen = getFromSharedPreferences("Terminal_access").toBoolean()
                bottomNavigationView.menu.findItem(R.id.sales).isEnabled = isOpen
            }
        }
    }

    private fun updateSalesOverlayVisibility() {
        when (merchantType) {
            MerchantType.ONLINE, MerchantType.GROUP -> {
                bottomNavigationView.menu.findItem(R.id.sales).isEnabled = true
            }
            MerchantType.CARDPRESENT -> {
                val isOpen = getFromSharedPreferences("Terminal_access").toBoolean()
                bottomNavigationView.menu.findItem(R.id.sales).isEnabled = isOpen
            }
        }
    }

    private suspend fun fetchTransactionData() {
        if (isLoading || !hasMoreData) {
            hideLoader()
            return
        }

        withContext(Dispatchers.Main) {
            showLoader()
        }

        isLoading = true

        when (currentTab) {
            TransactionTab.CARD_PRESENT -> fetchCardPresentTransactions()
            TransactionTab.ECOMMERCE -> fetchEcommerceTransactions()
        }
    }

    private fun fetchCardPresentTransactions() {
        val terminalId = getUserTerminalData()?.terminalId

        if (terminalId != null) {
            fetchTransactionDataForCurrentDay(
                context = this,
                lifecycleScope = lifecycleScope,
                sort = "-date_time",
                page = currentPage,
                terminalId = terminalId
            ) { transactions, paginationData ->
                try {
                    isLoading = false

                    if (!transactions.isNullOrEmpty()) {
                        val newTransactions = mapCardPresentTransactions(transactions)

                        if (currentPage == 1) {
                            cardPresentTransactions.clear()
                        } else {
                            Log.d(
                                "HomeScreen",
                                "Pagination - adding to existing card present transactions"
                            )
                        }

                        val currentSize = cardPresentTransactions.size

                        cardPresentTransactions.addAll(newTransactions)

                        val recyclerView: RecyclerView =
                            findViewById(R.id.hs_transaction_recycler_view)
                        val emptyStateLayout: LinearLayout = findViewById(R.id.empty_state_layout)

                        recyclerView.visibility = View.VISIBLE
                        emptyStateLayout.visibility = View.GONE

                        if (recyclerView.adapter != cardPresentAdapter) {
                            recyclerView.adapter = cardPresentAdapter
                        }

                        cardPresentAdapter.notifyItemRangeInserted(
                            currentSize,
                            newTransactions.size
                        )

                        updateUIWithTransactions(cardPresentTransactions, paginationData)
                    } else {
                        if (currentPage == 1) {
                            handleEmptyState()
                        } else {
                            hasMoreData = false
                            hideLoader()
                        }
                    }
                } catch (e: Exception) {
                    hideLoader()
                    Toast.makeText(
                        this,
                        "Failed to load transactions: ${e.message}",
                        Toast.LENGTH_LONG
                    ).show()
                }
            }
        } else {
            isLoading = false
            handleEmptyState()
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private suspend fun fetchEcommerceTransactions() {
        val userId = getUserParentData()?.id ?: return

        val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        val today = dateFormat.format(Calendar.getInstance().time)

        fetchEcommerceTransactionData(
            context = this,
            page = currentPage,
            userId = userId,
            startDate = today,
            endDate = today
        ) { transactionList ->
            try {
                isLoading = false

                if (transactionList != null && transactionList.transactions.isNotEmpty()) {
                    val newTransactions = mapEcommerceTransactions(transactionList.transactions)

                    if (currentPage == 1) {
                        ecommerceTransactions.clear()
                    }

                    val startPosition = ecommerceTransactions.size
                    ecommerceTransactions.addAll(newTransactions)

                    val recyclerView: RecyclerView = findViewById(R.id.hs_transaction_recycler_view)
                    val emptyStateLayout: LinearLayout = findViewById(R.id.empty_state_layout)

                    recyclerView.visibility = View.VISIBLE
                    emptyStateLayout.visibility = View.GONE

                    if (recyclerView.adapter != ecommerceAdapter) {
                        recyclerView.adapter = ecommerceAdapter
                    }

                    if (currentPage == 1) {
                        ecommerceAdapter.notifyDataSetChanged()
                    } else {
                        ecommerceAdapter.notifyItemRangeInserted(
                            startPosition,
                            newTransactions.size
                        )
                    }

                    updateUIWithTransactions(ecommerceTransactions, transactionList)
                } else {
                    if (currentPage == 1) {
                        handleEmptyState()
                    } else {
                        hasMoreData = false
                        hideLoader()
                    }
                }
            } catch (e: Exception) {
                hideLoader()
                Toast.makeText(
                    this,
                    "Failed to load transactions: ${e.message}",
                    Toast.LENGTH_LONG
                ).show()
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun resetAndFetchTransactions() {
        showLoader()
        currentPage = 1
        hasMoreData = true

        when (currentTab) {
            TransactionTab.CARD_PRESENT -> {
                cardPresentTransactions.clear()
                cardPresentAdapter.notifyDataSetChanged()
            }

            TransactionTab.ECOMMERCE -> {
                ecommerceTransactions.clear()
                ecommerceAdapter.notifyDataSetChanged()
            }
        }

        val recyclerView: RecyclerView = findViewById(R.id.hs_transaction_recycler_view)

        val adapter = when (currentTab) {
            TransactionTab.CARD_PRESENT -> cardPresentAdapter
            TransactionTab.ECOMMERCE -> ecommerceAdapter
        }

        recyclerView.adapter = adapter

        lifecycleScope.launch {
            fetchTransactionData()
        }
    }

    private fun mapCardPresentTransactions(transactions: List<BusinessDayTransaction>): List<TransactionItem.Transaction> {
        return transactions.map { transaction ->
            TransactionItem.Transaction(
                transactionId = transaction.id,
                rrn = transaction.rrn,
                cardMask = transaction.card,
                amount = transaction.amount,
                currencySymbol = getCurrencySymbolById(transaction.currency_id),
                dateTime = transaction.date_time,
                transactionStatus = transaction.mpos_transaction_status_id,
                originTransactionId = transaction.origin_trx_id
            )
        }
    }

    private fun mapEcommerceTransactions(transactions: List<EcommerceTransaction>): List<EcommerceTransactionItem> {
        return transactions
            .filter { it.status != "CREATED" }
            .map { transaction ->
                val amountDecimal = transaction.amount.toDouble() / 100.0
                val formattedAmount = String.format(Locale.US, "%.2f", amountDecimal)
                val item = EcommerceTransactionItem(
                    transactionId = transaction.id,
                    externalId = transaction.externalId,
                    amount = formattedAmount,
                    currencySymbol = transaction.currency.toString(),
                    dateTime = transaction.createdAt?.toString() ?: "",
                    status = transaction.status
                )

                item
            }
    }

    private fun updateUIWithTransactions(
        currentList: List<Any>,
        paginationData: Any?
    ) {
        val incomeValueMain: TextView = findViewById(R.id.income_value_main)
        val incomeValueDecimal: TextView = findViewById(R.id.income_value_decimal)
        val txtCurrency: TextView = findViewById(R.id.txt_currency_home)

        val totalIncome = when (currentTab) {
            TransactionTab.CARD_PRESENT -> {
                (currentList as List<TransactionItem.Transaction>)
                    .filter { it.transactionStatus == 1 && it.originTransactionId == 0 }
                    .sumOf { it.amount.toDouble() }
            }

            TransactionTab.ECOMMERCE -> {
                (currentList as List<EcommerceTransactionItem>)
                    .filter { it.status != "CREATED" }
                    .sumOf { it.amount.toDouble() }
            }
        }

        val incomeString = String.format(Locale.US, "%.2f", totalIncome)
        val (mainPart, decimalPart) = incomeString.split(".")

        txtCurrency.text = getUserParentData()?.currency ?: "EUR"
        incomeValueMain.text = mainPart
        incomeValueDecimal.text = ".$decimalPart"


        hasMoreData = try {
            when (paginationData) {
                is EcommerceTransactionList -> {
                    val currentPage = paginationData.paginationCurrentPage.toIntOrNull() ?: 0
                    val pageCount = paginationData.paginationPageCount.toIntOrNull() ?: 0
                    currentPage < pageCount
                }

                is PaginationData -> {
                    paginationData.currentPage < paginationData.pageCount
                }

                else -> false
            }
        } catch (_: Exception) {
            false
        }
        hideLoader()
    }

    private fun handleEmptyState() {
        val recyclerView: RecyclerView = findViewById(R.id.hs_transaction_recycler_view)
        val emptyStateLayout: LinearLayout = findViewById(R.id.empty_state_layout)
        val incomeValueMain: TextView = findViewById(R.id.income_value_main)
        val incomeValueDecimal: TextView = findViewById(R.id.income_value_decimal)
        val txtCurrency: TextView = findViewById(R.id.txt_currency_home)

        val isEmpty = when (currentTab) {
            TransactionTab.CARD_PRESENT -> cardPresentTransactions.isEmpty()
            TransactionTab.ECOMMERCE -> ecommerceTransactions.isEmpty()
        }

        if (isEmpty) {
            recyclerView.visibility = View.GONE
            emptyStateLayout.visibility = View.VISIBLE

            incomeValueMain.text = "0"
            incomeValueDecimal.text = ".00"
            txtCurrency.text = getUserParentData()?.currency ?: "EUR"
            hasMoreData = false

        } else {
            recyclerView.visibility = View.VISIBLE
            emptyStateLayout.visibility = View.GONE

            if (currentTab == TransactionTab.ECOMMERCE && recyclerView.adapter != ecommerceAdapter) {
                recyclerView.adapter = ecommerceAdapter
            } else if (currentTab == TransactionTab.CARD_PRESENT && recyclerView.adapter != cardPresentAdapter) {
                recyclerView.adapter = cardPresentAdapter
            }

        }
        hideLoader()
    }

    private suspend fun checkAndDisplayBusinessDayStatus(context: Context): Pair<Boolean, Int> {
        val userTerminalData = getUserTerminalData()
        val terminalId = userTerminalData?.terminalName

        val businessDayStatus = terminalId?.let {
            fetchBusinessDayStatus(
                context,
                MERCHANT_BANK_ID,
                "merchant_email",
                getUserParentData()?.email!!,
                "merchant_terminal_id",
                it
            )
        }

        return withContext(Dispatchers.Main) {
            val terminalStatus: TextView = findViewById(R.id.txt_business_day_status)

            if (businessDayStatus != null) {
                swipeRefreshLayout.isRefreshing = false
                when (businessDayStatus.data?.code) {
                    4 -> {
                        terminalStatus.visibility = View.GONE
                        Pair(false, -1)
                    }

                    1 -> {
                        terminalStatus.visibility = View.GONE
                        Pair(true, -1)
                    }

                    else -> {
                        terminalStatus.visibility = View.VISIBLE
                        terminalStatus.text = getString(R.string.business_closing_ip)
                        terminalStatus.setTextColor(
                            ContextCompat.getColor(
                                context,
                                R.color.transaction_card_red
                            )
                        )
                        Pair(false, businessDayStatus.data?.code ?: -1)
                    }
                }
            } else {
                swipeRefreshLayout.isRefreshing = false
                Pair(false, -1)
            }
        }
    }

    private suspend fun getAndStoreUserPEDDetails(context: Context) {
        val deviceId =
            Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID)
        val terminalStatus: TextView = findViewById(R.id.txt_connection_status_value)
        val terminalInfo: LinearLayout = findViewById(R.id.terminal_info)
        val terminalName: TextView = findViewById(R.id.txt_terminal_name_value)
        val terminalBtn: ImageButton = findViewById(R.id.btn_terminal)
        val terminalSection: CardView = findViewById(R.id.cardView1)

        if (merchantType == MerchantType.ONLINE) {
            terminalSection.visibility = View.GONE
            saveToSharedPreferences("Terminal_access", "false")
            updateSalesOverlayVisibility()
            return
        } else {
            terminalSection.visibility = View.VISIBLE
        }

        val (isPEDLinked, pedData) = fetchAndStoreUserData(context)
        val userTerminalData = getUserTerminalData()

        if (!isPEDLinked) {
            saveToSharedPreferences("Terminal_access", "false")
            updateSalesOverlayVisibility()
            setupUnpairedDeviceUI(terminalBtn, terminalStatus, terminalInfo)
            terminalBtn.setOnClickListener {
                startTerminalLinkProcess(context, userTerminalData)
            }
        } else {
            val pedDeviceId = pedData?.merchant_device_id?.substringBefore("_")
            if (pedDeviceId == deviceId) {
                setupPairedDeviceUI(
                    terminalBtn,
                    terminalStatus,
                    terminalInfo,
                    pedData,
                    terminalName
                )
                saveToSharedPreferences("Terminal_access", "true")
                updateSalesOverlayVisibility()
            } else {
                saveToSharedPreferences("Terminal_access", "false")
                updateSalesOverlayVisibility()
                setupUnpairedDeviceUI(terminalBtn, terminalStatus, terminalInfo)
                terminalBtn.setOnClickListener {
                    startReLinkProcess(context, pedData)
                }
            }
        }
    }

    private fun setupUnpairedDeviceUI(
        terminalBtn: ImageButton,
        terminalStatus: TextView,
        terminalInfo: LinearLayout
    ) {
        terminalBtn.visibility = View.VISIBLE
        terminalBtn.background = AppCompatResources.getDrawable(this, R.drawable.link_icon)
        terminalStatus.setTextColor(getColor(R.color.red))
        terminalStatus.text = getString(R.string.unpaired)
        terminalInfo.visibility = View.GONE
    }

    private fun setupPairedDeviceUI(
        terminalBtn: ImageButton,
        terminalStatus: TextView,
        terminalInfo: LinearLayout,
        pedData: PedStatusResponse?,
        terminalNameTextView: TextView
    ) {
        terminalStatus.setTextColor(getColor(R.color.lavender))
        terminalBtn.background = AppCompatResources.getDrawable(this, R.drawable.unlink_icon)
        terminalStatus.text = getString(R.string.paired)
        terminalInfo.visibility = View.VISIBLE
        terminalNameTextView.text = pedData?.merchant_terminal_id
        terminalBtn.setOnClickListener {
            showUnlinkPaymentTerminalDialog(pedData)
        }
    }

    private fun showUnlinkPaymentTerminalDialog(pedData: PedStatusResponse?) {
        val dialogOverlay = findViewById<FrameLayout>(R.id.dialog_unlink_payment_terminal)
        val confirmButton = findViewById<Button>(R.id.confirm_unbind_button)
        val cancelButton = findViewById<Button>(R.id.cancel_unbind_button)
        val confirmButtonLoader = findViewById<ProgressBar>(R.id.confirm_unbind_button_loader)

        disableDialogButton(confirmButton, confirmButtonLoader)
        dialogOverlay.visibility = View.VISIBLE

        lifecycleScope.launch {
            val (isOpen, statusCode) = withContext(Dispatchers.IO) {
                checkAndDisplayBusinessDayStatus(this@HomeScreen)
            }

            if (isOpen) {
                enableDialogButton(confirmButton, confirmButtonLoader)
                confirmButton.text = getString(R.string.close_business_day)
                confirmButton.setOnClickListener {
                    handleCloseBusinessDay(
                        pedData,
                        dialogOverlay,
                        confirmButton,
                        confirmButtonLoader
                    )
                }
            } else {
                if (statusCode != -1) {
                    disableDialogButton(confirmButton, confirmButtonLoader)
                } else {
                    enableDialogButton(confirmButton, confirmButtonLoader)
                    confirmButton.text = getString(R.string.unbind)
                    confirmButton.setOnClickListener {
                        handleUnbindTerminal(
                            pedData,
                            dialogOverlay,
                            confirmButton,
                            confirmButtonLoader
                        )
                    }
                }
            }
        }

        cancelButton.setOnClickListener {
            dialogOverlay.visibility = View.GONE
        }
    }

    private fun handleCloseBusinessDay(
        pedData: PedStatusResponse?,
        dialogOverlay: FrameLayout,
        confirmButton: Button,
        confirmButtonLoader: ProgressBar
    ) {
        saveToSharedPreferences("Terminal_access", "false")
        updateSalesOverlayVisibility()
        confirmButton.isEnabled = false
        dialogOverlay.visibility = View.GONE
        showLoader()

        lifecycleScope.launch {
            if (pedData != null) {
                disableDialogButton(confirmButton, confirmButtonLoader)
                val closeResult = withContext(Dispatchers.IO) {
                    closeBusinessDay(
                        this@HomeScreen,
                        MERCHANT_BANK_ID,
                        "merchant_email",
                        getUserParentData()?.email!!,
                        "merchant_terminal_id",
                        pedData.merchant_terminal_id
                    )
                }

                if (closeResult) {
                    hideLoader()
                    confirmButton.isEnabled = true
                    lifecycleScope.launch {
                        val (isOpenNow, code) = withContext(Dispatchers.IO) {
                            checkAndDisplayBusinessDayStatus(this@HomeScreen)
                        }
                        if (!isOpenNow && code == -1) {
                            refreshTransactionList()
                        }
                    }
                    enableDialogButton(confirmButton, confirmButtonLoader)
                } else {
                    hideLoader()
                    Toast.makeText(
                        this@HomeScreen,
                        "Failed to close business day",
                        Toast.LENGTH_LONG
                    ).show()
                    confirmButton.isEnabled = true
                    saveToSharedPreferences("Terminal_access", "true")
                    updateSalesOverlayVisibility()
                    enableDialogButton(confirmButton, confirmButtonLoader)
                }
            }
        }
    }

    private fun handleUnbindTerminal(
        pedData: PedStatusResponse?,
        dialogOverlay: FrameLayout,
        confirmButton: Button,
        confirmButtonLoader: ProgressBar
    ) {
        saveToSharedPreferences("Terminal_access", "false")
        updateSalesOverlayVisibility()
        confirmButton.isEnabled = false
        dialogOverlay.visibility = View.GONE
        showLoader()

        lifecycleScope.launch {
            if (pedData != null) {
                disableDialogButton(confirmButton, confirmButtonLoader)
                val unbindSuccess = withContext(Dispatchers.IO) {
                    unbindPaymentTerminal(
                        this@HomeScreen,
                        MERCHANT_BANK_ID,
                        "merchant_email",
                        getUserParentData()?.email!!,
                        "merchant_terminal_id",
                        pedData.merchant_terminal_id
                    )
                }

                if (unbindSuccess) {
                    confirmButton.isEnabled = true
                    lifecycleScope.launch {
                        getAndStoreUserPEDDetails(this@HomeScreen)
                    }
                    hideLoader()
                    enableDialogButton(confirmButton, confirmButtonLoader)
                } else {
                    hideLoader()
                    Toast.makeText(
                        this@HomeScreen,
                        "Failed to unbind payment terminal",
                        Toast.LENGTH_LONG
                    ).show()
                    confirmButton.isEnabled = true
                    saveToSharedPreferences("Terminal_access", "true")
                    updateSalesOverlayVisibility()
                    enableDialogButton(confirmButton, confirmButtonLoader)
                }
            }
        }
    }

    private fun startTerminalLinkProcess(context: Context, userTerminalData: PaymentTerminal?) {
        updateUIWithMessage("Processing")
        showSDKStatus()

        lifecycleScope.launch {
            val authToken = fetchAuthToken(context) ?: return@launch
            val terminalId = userTerminalData?.terminalName ?: return@launch
            val (success, connectionToken) = fetchConnectionToken(
                this@HomeScreen,
                authToken,
                terminalId,
                getUserParentData()?.email!!,
                "LINK"
            )

            if (!success || connectionToken == null) {
                hideSDKStatus()
                return@launch
            }
            startSdkInitialization(connectionToken)
        }
    }

    private fun startReLinkProcess(context: Context, pedData: PedStatusResponse?) {
        updateUIWithMessage("Processing")
        showSDKStatus()

        lifecycleScope.launch {
            val authToken = fetchAuthToken(context) ?: return@launch
            if (pedData != null && pedData.merchant_terminal_id != null) {
                val (success, connectionToken) = fetchConnectionToken(
                    this@HomeScreen,
                    authToken,
                    pedData.merchant_terminal_id,
                    getUserParentData()?.email!!,
                    "RELINK"
                )
                if (!success || connectionToken == null) {
                    runOnUiThread {
                        updateUIWithMessage(
                            "Failed to bind device to payment terminal. Please unbind the previous device and try again"
                        )
                        findViewById<ProgressBar>(R.id.sdk_status_progress).visibility = View.GONE
                        closeButton.visibility = View.VISIBLE
                    }
                    return@launch
                }
                startSdkInitialization(connectionToken)
            }
        }
    }

    private fun startSdkInitialization(connectionToken: String?) {
        updateUIWithMessage("Initializing Device")

        SdkUtils.startSDKMethod(
            merchantBankId = MERCHANT_BANK_ID,
            appLanguageCode = APP_LANGUAGE_CODE,
            solutionPartnerId = SOLUTION_PARTNER_ID,
            context = this,
            activityContext = this
        ) { startResponse ->
            val responseMessage = ResponseCodes.responseMessages[startResponse?.responseCode]

            when (startResponse?.responseCode) {
                "910" -> {
                    configureDeviceSetup(connectionToken)
                }

                "0" -> {
                    hideSDKStatus()
                }

                else -> {
                    runOnUiThread {
                        updateUIWithMessage(
                            "Initialization failed: ${
                                responseMessage ?: "Unknown error, Code: ${startResponse?.description}"
                            }"
                        )
                        findViewById<ProgressBar>(R.id.sdk_status_progress).visibility = View.GONE
                        closeButton.visibility = View.VISIBLE
                    }
                }
            }
        }
    }

    private fun configureDeviceSetup(connectionToken: String?) {
        updateUIWithMessage("Configuring Device to Accept Payment")

        SdkUtils.deviceSetupSDKMethod(
            merchantBankId = MERCHANT_BANK_ID,
            appLanguageCode = APP_LANGUAGE_CODE,
            solutionPartnerId = SOLUTION_PARTNER_ID,
            connectionToken = connectionToken ?: "",
            context = this
        ) { setupResponse ->
            val setupResponseMessage = ResponseCodes.responseMessages[setupResponse?.responseCode]
            runOnUiThread {
                if (setupResponse?.responseCode == "909") {
                    saveToSharedPreferences("Terminal_access", "true")
                    updateSalesOverlayVisibility()
                    updateUIWithMessage("Almost Done")

                    SdkUtils.startSDKMethod(
                        merchantBankId = MERCHANT_BANK_ID,
                        appLanguageCode = APP_LANGUAGE_CODE,
                        solutionPartnerId = SOLUTION_PARTNER_ID,
                        context = this,
                        activityContext = this
                    ) {
                        hideSDKStatus()
                    }

                    lifecycleScope.launch {
                        getAndStoreUserPEDDetails(this@HomeScreen)
                    }
                } else {
                    updateUIWithMessage(
                        "Device Configuration failed: ${
                            setupResponseMessage ?: "Unknown error, Code: ${setupResponse?.description}"
                        }"
                    )
                    closeButton.visibility = View.VISIBLE
                    findViewById<ProgressBar>(R.id.sdk_status_progress).visibility = View.GONE
                }
            }
        }
    }

    private fun showLoader() {
        findViewById<View>(R.id.loading_overlay).visibility = View.VISIBLE
    }

    private fun hideLoader() {
        findViewById<View>(R.id.loading_overlay).visibility = View.GONE
    }

    private fun updateUIWithMessage(message: String) {
        runOnUiThread {
            findViewById<ProgressBar>(R.id.sdk_status_progress).visibility = View.VISIBLE
            findViewById<TextView>(R.id.sdk_status_message).text = message
        }
    }

    private fun showSDKStatus() {
        runOnUiThread {
            findViewById<View>(R.id.sdk_status_overlay).visibility = View.VISIBLE
            findViewById<ProgressBar>(R.id.sdk_status_progress).visibility = View.VISIBLE
            closeButton.visibility = View.GONE
        }
    }

    private fun hideSDKStatus() {
        runOnUiThread {
            findViewById<ProgressBar>(R.id.sdk_status_progress).visibility = View.GONE
            findViewById<View>(R.id.sdk_status_overlay).visibility = View.GONE
            closeButton.visibility = View.GONE
        }
    }

    private fun disableDialogButton(button: Button, loader: ProgressBar) {
        button.isEnabled = false
        button.text = ""
        button.setBackgroundColor(ContextCompat.getColor(this@HomeScreen, R.color.gray))
        loader.visibility = View.VISIBLE
    }

    private fun enableDialogButton(button: Button, loader: ProgressBar) {
        button.isEnabled = true
        button.setBackgroundColor(ContextCompat.getColor(this@HomeScreen, R.color.lavender))
        loader.visibility = View.GONE
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun refreshTransactionList() {
        if (!isSessionValid(this)) {
            handleSessionExpiry(this)
        } else {
            showLoader()
            currentPage = 1
            hasMoreData = true
            isLoading = false

            when (currentTab) {
                TransactionTab.CARD_PRESENT -> {
                    cardPresentTransactions.clear()
                    cardPresentAdapter.notifyDataSetChanged()
                }

                TransactionTab.ECOMMERCE -> {
                    ecommerceTransactions.clear()
                    ecommerceAdapter.notifyDataSetChanged()
                }
            }

            val recyclerView: RecyclerView = findViewById(R.id.hs_transaction_recycler_view)

            val adapter = when (currentTab) {
                TransactionTab.CARD_PRESENT -> cardPresentAdapter
                TransactionTab.ECOMMERCE -> ecommerceAdapter
            }

            recyclerView.adapter = adapter

            lifecycleScope.launch {
                fetchTransactionData()
            }
        }
    }
}