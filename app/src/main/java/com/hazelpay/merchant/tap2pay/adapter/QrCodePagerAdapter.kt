package com.hazelpay.merchant.tap2pay.adapter

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.hazelpay.merchant.tap2pay.fragments.StaticQrCodeFragment

class QrCodePagerAdapter(activity: FragmentActivity) : FragmentStateAdapter(activity) {

    override fun getItemCount(): Int = 1

    override fun createFragment(position: Int): Fragment {
        return when (position) {
            0 -> StaticQrCodeFragment.Companion.newInstance()
            else -> throw IllegalArgumentException("Invalid position: $position")
        }
    }
}