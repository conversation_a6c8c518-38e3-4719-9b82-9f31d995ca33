package com.hazelpay.merchant.tap2pay.network

import com.hazelpay.merchant.tap2pay.model.ApiResponse
import com.hazelpay.merchant.tap2pay.model.AuthRequest
import com.hazelpay.merchant.tap2pay.model.BindDeviceTokenResponse
import com.hazelpay.merchant.tap2pay.model.BusinessDayRequest
import com.hazelpay.merchant.tap2pay.model.BusinessDayResponse
import com.hazelpay.merchant.tap2pay.model.CurrencyResponse
import com.hazelpay.merchant.tap2pay.model.EcommerceRequest
import com.hazelpay.merchant.tap2pay.model.EcommerceTransaction
import com.hazelpay.merchant.tap2pay.model.EcommerceTransactionList
import com.hazelpay.merchant.tap2pay.model.LoginData
import com.hazelpay.merchant.tap2pay.model.LoginRequest
import com.hazelpay.merchant.tap2pay.model.OnboardingRequest
import com.hazelpay.merchant.tap2pay.model.PaymentRequest
import com.hazelpay.merchant.tap2pay.model.PaymentResponse
import com.hazelpay.merchant.tap2pay.model.PedStatusRequest
import com.hazelpay.merchant.tap2pay.model.PedStatusResponse
import com.hazelpay.merchant.tap2pay.model.QrCodeDetails
import com.hazelpay.merchant.tap2pay.model.QrCodeStatusUpdateResponse
import com.hazelpay.merchant.tap2pay.model.RefundRequestPayload
import com.hazelpay.merchant.tap2pay.model.ReverseTransactionPayload
import com.hazelpay.merchant.tap2pay.model.SecurityCredentialData
import com.hazelpay.merchant.tap2pay.model.StaticQrCode
import com.hazelpay.merchant.tap2pay.model.StaticQrCodeRequest
import com.hazelpay.merchant.tap2pay.model.SupportRequest
import com.hazelpay.merchant.tap2pay.model.TerminalParentData
import com.hazelpay.merchant.tap2pay.model.TransactionReceiptRequest
import com.hazelpay.merchant.tap2pay.model.TransactionRequest
import com.hazelpay.merchant.tap2pay.model.TransactionResponse
import com.hazelpay.merchant.tap2pay.model.UpdateUserRequest
import okhttp3.ResponseBody
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.PATCH
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Path
import retrofit2.http.Query

interface ApiService {
    @POST("onboarding/public/login")
    suspend fun login(@Body request: LoginRequest): ApiResponse<LoginData>

    @POST("card-present/public/authenticate")
    suspend fun authenticate(@Body request: AuthRequest): ApiResponse<String>

    @POST("card-present/public/receive-payment-token")
    suspend fun getPaymentToken(@Body payload: PaymentRequest): ApiResponse<PaymentResponse>

    @POST("card-present/public/bind-device")
    suspend fun bindDevice(
        @Header("Authorization") authHeader: String,
        @Body payload: Map<String, String>
    ): ApiResponse<BindDeviceTokenResponse>

    @PUT("card-present/public/reverse-transaction")
    suspend fun reverseTransaction(
        @Header("Authorization") token: String,
        @Body payload: ReverseTransactionPayload
    ): Response<Unit>

    @PUT("card-present/public/refund-transaction")
    suspend fun processRefund(
        @Header("Authorization") authToken: String,
        @Body payload: RefundRequestPayload
    ): Response<Unit>

    @POST("card-present/public/current-day-transactions")
    suspend fun getCurrentDayTransactions(
        @Header("Authorization") authorization: String,
        @Body transactionRequest: TransactionRequest
    ): TransactionResponse

    @GET("card-present/public/currency")
    suspend fun getCurrencyDetails(
        @Header("Authorization") authorization: String
    ): CurrencyResponse

    @POST("card-present/public/transaction-archive")
    suspend fun fetchArchivedTransactions(
        @Header("Authorization") authorization: String,
        @Body request: TransactionRequest
    ): TransactionResponse

    @GET("card-present/api/payment-terminals/{userId}")
    suspend fun getPaymentTerminalDetails(
        @Header("Authorization") authorization: String,
        @Path("userId") userId: String
    ): Response<ApiResponse<TerminalParentData>>

    @PUT("card-present/public/ped/status")
    suspend fun getPedStatus(
        @Header("Authorization") authToken: String,
        @Body request: PedStatusRequest
    ): Response<ApiResponse<PedStatusResponse>>

    @PUT("card-present/public/unbind-device")
    suspend fun unbindDevice(
        @Header("Authorization") authToken: String,
        @Body request: PedStatusRequest
    ): Response<ResponseBody>

    @PUT("card-present/public/business-day/status")
    suspend fun getBusinessDayStatus(
        @Header("Authorization") token: String,
        @Body request: BusinessDayRequest
    ): Response<ApiResponse<BusinessDayResponse>>

    @PUT("card-present/public/business-day/close")
    suspend fun closeBusinessDay(
        @Header("Authorization") token: String,
        @Body request: BusinessDayRequest
    ): Response<ApiResponse<Unit>>

    @POST("onboarding/public/onboarding-request")
    suspend fun sendOnboardingRequest(
        @Body request: OnboardingRequest
    ): Response<Unit>

    @POST("onboarding/api/create-issue")
    suspend fun createIssue(
        @Header("Authorization") token: String,
        @Body issueRequest: SupportRequest
    ): Response<ApiResponse<String>>

    @POST("card-present/api/send-transaction-receipt")
    suspend fun sendTransactionReceipt(
        @Header("Authorization") authorization: String,
        @Body request: TransactionReceiptRequest
    ): Response<ApiResponse<Unit>>

    @PATCH("onboarding/api/user/update")
    suspend fun updateUserName(
        @Body request: UpdateUserRequest,
        @Header("Authorization") authToken: String
    ): Response<Unit>

    @GET("onboarding/api/security-credentials/active")
    suspend fun getActiveCredentials(
        @Header("Authorization") token: String,
        @Query("userId") userId: String
    ): ApiResponse<SecurityCredentialData>

    @GET("online/public/transaction")
    suspend fun getTransactionStatus(
        @Query("transactionId") transactionId: String
    ): ApiResponse<EcommerceTransaction>

    @GET("online/jwt/api/user-transactions")
    suspend fun getEcommerceTransactions(
        @Header("Authorization") token: String,
        @Query("userId") userId: String,
        @Query("page") page: Int,
        @Query("startDate") startDate: String?,
        @Query("endDate") endDate: String?
    ): ApiResponse<EcommerceTransactionList>

    @POST("online/api/payment/create-qr-payment")
    suspend fun createQRCodePayment(
        @Header("API-Key") apiKey: String,
        @Header("API-Secret") apiSecret: String,
        @Query("autoCapture") autoCapture: Boolean,
        @Body request: EcommerceRequest
    ): Response<ApiResponse<String>>

    @GET("online/jwt/api/qrcode-details")
    suspend fun getQrCodeDetails(
        @Header("Authorization") token: String,
        @Query("qrcodeId") qrcodeId: String
    ): ApiResponse<QrCodeDetails>

    @PATCH("online/jwt/api/qrcode-status")
    suspend fun updateQrCodeStatus(
        @Header("Authorization") token: String,
        @Query("qrcodeId") qrcodeId: String,
        @Query("status") status: String
    ): ApiResponse<QrCodeStatusUpdateResponse>

    @GET("online/jwt/api/static-qr")
    suspend fun getStaticQrCodes(
        @Header("Authorization") token: String,
        @Query("parentUserId") parentUserId: String
    ): ApiResponse<List<StaticQrCode>>

    @POST("online/jwt/api/static-qr")
    suspend fun createStaticQrCode(
        @Header("Authorization") token: String,
        @Body request: StaticQrCodeRequest
    ): ApiResponse<StaticQrCode>
}
