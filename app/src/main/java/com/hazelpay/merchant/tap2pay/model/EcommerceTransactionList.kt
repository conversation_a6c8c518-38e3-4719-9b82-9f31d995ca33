package com.hazelpay.merchant.tap2pay.model

import com.google.gson.annotations.SerializedName

data class EcommerceTransactionList (
    @SerializedName("transactions") val transactions: List<EcommerceTransaction>,
    @SerializedName("paginationTotalCount") val paginationTotalCount: String,
    @SerializedName("paginationPageCount") val paginationPageCount: String,
    @SerializedName("paginationCurrentPage") val paginationCurrentPage: String,
    @SerializedName("paginationPerPage") val paginationPerPage: String
)