package com.hazelpay.merchant.tap2pay.utils

object CurrencyConstants {
    val CURRENCY_SYMBOLS = arrayOf(
        Pair(5, "ORB"),
        <PERSON>ir(6, "STR"),
        <PERSON>ir(8, "ALL"),
        <PERSON>ir(36, "AUD"),
        <PERSON><PERSON>(111, "TST"),
        <PERSON><PERSON>(124, "CAD"),
        <PERSON><PERSON>(156, "CNY"),
        <PERSON><PERSON>(203, "CZK"),
        <PERSON><PERSON>(208, "DKK"),
        <PERSON><PERSON>(348, "HUF"),
        <PERSON>ir(398, "KZT"),
        <PERSON><PERSON>(498, "MDL"),
        <PERSON>ir(566, "NGN"),
        <PERSON><PERSON>(578, "NOK"),
        <PERSON><PERSON>(586, "PKR"),
        <PERSON><PERSON>(643, "RUB"),
        <PERSON><PERSON>(710, "ZAR"),
        <PERSON><PERSON>(752, "SEK"),
        <PERSON><PERSON>(756, "CHF"),
        <PERSON>ir(784, "AED"),
        <PERSON><PERSON>(826, "GBP"),
        <PERSON><PERSON>(840, "USD"),
        <PERSON><PERSON>(933, "BYN"),
        <PERSON><PERSON>(941, "RSD"),
        <PERSON><PERSON>(944, "AZN"),
        <PERSON><PERSON>(946, "RON"),
        <PERSON><PERSON>(974, "BYR"),
        <PERSON><PERSON>(975, "BGN"),
        Pair(978, "EUR"),
        Pair(980, "UAH"),
        Pair(981, "GEL"),
        Pair(985, "PLN")
    )
}

object ResponseCodes {
    val responseMessages = mapOf(
        "0" to "Transaction completed successfully",
        "512" to "Device blocked due to security reasons. See security checks here. Correct non-compliance and request to unlock.",
        "900" to "Transaction failed. Notify the merchant that the transaction was not completed.",
        "999" to "Transaction status is unknown. Notify the merchant and reverse payment if needed.",
        "903" to "Invalid merchant_bank_id. Verify the merchant_bank_id parameter value.",
        "904" to "Network Error. Ask the user to check their network settings and try again.",
        "905" to "Invalid transaction type. Check supported payment types for the provider.",
        "906" to "Invalid locale. Check supported languages for the provider.",
        "907" to "Invalid amount. Ensure the amount is not zero.",
        "908" to "Overlay detected. Device does not meet Trusted Device Criteria.",
        "909" to "Device successfully configured and ready for payment.",
        "910" to "Device setup required. Complete the setup before initiating payments.",
        "911" to "Developer mode detected. Device does not meet Trusted Device Criteria.",
        "912" to "Split screen detected. Device does not meet Trusted Device Criteria.",
        "913" to "Request formation error. Contact the acquiring service provider.",
        "914" to "Encryption error. Contact the acquiring service provider.",
        "915" to "Error receiving a response from the server. Contact the provider.",
        "916" to "Response parsing error. Contact the provider.",
        "917" to "Invalid currency. Check supported currencies for the merchant.",
        "918" to "Invalid payload data. Check parameter values and token format.",
        "919" to "JNI error. Contact the acquiring service provider.",
        "920" to "Payment declined by server. Display server_message with decline details.",
        "921" to "Transaction canceled due to refusal to enter the signature.",
        "922" to "External data missing in the token. Verify the 'data' parameter.",
        "923" to "Reset application settings required.",
        "924" to "Incorrect device status. Contact the acquiring service provider.",
        "925" to "Service connection error. Unable to reach an internal service.",
        "950" to "No Internet connection. Unable to proceed without network.",
        "951" to "All required permissions have not been obtained. The application lacks necessary permissions for SDK operation.",
        "952" to "Configuration server response took too long. This prevents SDK operation. Contact the acquiring service provider.",
        "953" to "Hardware TEE is not detected on the device. Device does not meet Trusted Device Criteria.",
        "954" to "Google services not detected. Device does not meet Trusted Device Criteria.",
        "955" to "Random generator self-check failed. Device does not meet Trusted Device Criteria.",
        "956" to "Device status error. General tapXphone software error. Contact the acquiring service provider.",
        "958" to "Reinitialization required. Complete device setup before initiating transactions.",
        "959" to "NFC not detected on the device. Unable to proceed with contactless transactions.",
        "961" to "Device initialization required. Complete setup before proceeding.",
        "962" to "Device blocked due to security reasons. See security checks and correct the non-compliance.",
        "963" to "Server response error. The operation resulted in an error on the server.",
        "964" to "Input parameters are incorrect. Verify request parameters.",
        "965" to "Required functionality is not supported by the SDK."
    )
}

val countries = arrayOf(
    "Select", "Austria", "Belgium", "Bulgaria", "Croatia", "Cyprus", "Czechia", "Denmark", "Estonia", "Finland",
    "France", "Germany", "Greece", "Hungary", "Ireland", "Italy", "Latvia", "Lithuania", "Luxembourg", "Malta",
    "Netherlands", "Poland", "Portugal", "Romania", "Slovakia", "Slovenia", "Spain", "Sweden", "United Kingdom"
)

val expectedTransactionsPerMonth = arrayOf(
    "Select" to "Select",
    "Less than 100,000" to "Less than 100,000",
    "Up to 500,000" to "Up to 500,000",
    "Up to 1,000,000" to "Up to 1,000,000",
    "More than 1,000,000" to "More than 1,000,000"
).toMap()

val expectedMonthlyActiveTerminals = arrayOf(
    "Select" to "Select",
    "Less than 100" to "Less than 100",
    "Up to 500" to "Up to 500",
    "Up to 1000" to "Up to 1000",
    "More than 1000" to "More than 1000"
).toMap()

enum class MerchantType(val value: String) {
    ONLINE("ONLINE"),
    CARDPRESENT("CARDPRESENT"),
    GROUP("GROUP");

    companion object {
        fun fromString(value: String?): MerchantType {
            return when(value) {
                ONLINE.value -> ONLINE
                CARDPRESENT.value -> CARDPRESENT
                GROUP.value -> GROUP
                else -> GROUP // Default to GROUP if unknown
            }
        }
    }
}

val merchantTypes = mapOf(
    "Select" to "Select",
    "Online" to "ONLINE",
    "In-Store" to "CARDPRESENT",
    "Online/In-Store" to "GROUP"
)

val emailPattern = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$".toRegex()
val phonePattern = "^\\+\\d{10,15}$".toRegex()

enum class TransactionStatus(val value: String) {
    CREATED("CREATED"),
    IN_PROGRESS("IN_PROGRESS"),
    AUTHORISED("AUTHORISED"),
    CAPTURED("CAPTURED"),
    REFUND("REFUND"),
    CANCELLED("CANCELLED")
}

val SUCCESS_STATUSES = arrayOf(
    TransactionStatus.AUTHORISED.value,
    TransactionStatus.CAPTURED.value
)

enum class QrCodeStatus(val value: String) {
    CREATED("CREATED"),
    SCANNED("SCANNED"),
    REDEEMED("REDEEMED"),
    FAILED("FAILED")
}