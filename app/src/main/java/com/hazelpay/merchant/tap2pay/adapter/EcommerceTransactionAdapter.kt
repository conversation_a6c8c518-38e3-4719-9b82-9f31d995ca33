package com.hazelpay.merchant.tap2pay.adapter

import android.content.Context
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.hazelpay.merchant.tap2pay.R

data class EcommerceTransactionItem(
    val transactionId: String,
    val externalId: String?,
    val amount: String,
    val currencySymbol: String,
    val dateTime: String,
    val status: String
)

class EcommerceTransactionAdapter(
    private val transactions: MutableList<EcommerceTransactionItem>,
    private val context: Context
) : RecyclerView.Adapter<EcommerceTransactionAdapter.ViewHolder>() {


    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val amountText: TextView = itemView.findViewById(R.id.transaction_amount)
        val dateText: TextView = itemView.findViewById(R.id.transaction_date)
        val statusText: TextView = itemView.findViewById(R.id.transaction_status)
        val idText: TextView = itemView.findViewById(R.id.transaction_id)
//        val descriptionText: TextView = itemView.findViewById(R.id.transaction_description)
        val iconView: ImageView = itemView.findViewById(R.id.transaction_icon)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(context).inflate(R.layout.item_ecommerce_transaction, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val transaction = transactions[position]

        holder.idText.text = "${transaction.externalId}"

//        holder.descriptionText.text = context.getString(R.string.online_payment)

        holder.dateText.text = formatDateTime(transaction.dateTime)

        holder.amountText.text = context.getString(R.string.transaction_amount_placeholder, transaction.currencySymbol, transaction.amount)

        val (statusText, backgroundDrawable, iconResource) = when (transaction.status) {
            "CREATED" -> Triple("Created", R.drawable.status_created_background, R.drawable.transaction_arrows)
            "AUTHORISED" -> Triple("Authorised", R.drawable.status_authorised_background, R.drawable.transaction_arrows)
            "CAPTURED" -> Triple("Captured", R.drawable.status_captured_background, R.drawable.transaction_arrows)
            "REFUND" -> Triple("Refunded", R.drawable.status_refund_background, R.drawable.transaction_arrows_red)
            "CANCELLED" -> Triple("Cancelled", R.drawable.status_cancelled_background, R.drawable.transaction_arrows_red)
            else -> Triple("Unknown", R.drawable.status_created_background, R.drawable.transaction_arrows)
        }

        holder.statusText.text = statusText
        holder.statusText.background = ContextCompat.getDrawable(context, backgroundDrawable)
        holder.statusText.setTextColor(ContextCompat.getColor(context, android.R.color.white))

        holder.iconView.setImageResource(iconResource)
    }

    override fun getItemCount(): Int = transactions.size

    private fun formatDateTime(dateTimeStr: String): String {
        return try {
            val formatters = listOf(
                DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"),
                DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"),
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),
                DateTimeFormatter.ISO_LOCAL_DATE_TIME
            )

            var dateTime: LocalDateTime? = null
            for (formatter in formatters) {
                try {
                    dateTime = LocalDateTime.parse(dateTimeStr, formatter)
                    break
                } catch (_: Exception) {
                }
            }

            if (dateTime != null) {
                val outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")
                dateTime.format(outputFormatter)
            } else {
                dateTimeStr
            }
        } catch (_: Exception) {
            dateTimeStr
        }
    }
}