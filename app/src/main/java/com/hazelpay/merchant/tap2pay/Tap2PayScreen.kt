
package com.hazelpay.merchant.tap2pay

import android.content.Intent
import android.os.Bundle
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity

class Tap2PayScreen : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_tap2pay_screen)

        val backButton: ImageButton = findViewById(R.id.back_button)
        val nfcIcon: ImageView = findViewById(R.id.nfc_icon)
        val amountValueTextView: TextView = findViewById(R.id.amount_value)


        val amountValue = intent.getStringExtra("amount_value")
        amountValueTextView.text = amountValue

        backButton.setOnClickListener {
            startActivity(Intent(this, PaymentScreen::class.java))
            finish()
        }

        nfcIcon.setOnClickListener {
            startActivity(Intent(this, CardPinCode::class.java))
            finish()
        }

        val cancelButton: ImageButton = findViewById(R.id.cancel_button)
        cancelButton.setOnClickListener {
            showToast(getString(R.string.transaction_cancelled))
            val intent = Intent(this, HomeScreen::class.java)
            startActivity(intent)
        }
    }

    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }
}
