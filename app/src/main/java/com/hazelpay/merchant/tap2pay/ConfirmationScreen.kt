package com.hazelpay.merchant.tap2pay

import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.nfc.NfcAdapter
import android.nfc.Tag
import android.os.Bundle
import android.provider.Settings
import android.util.Log
import android.view.View
import android.view.WindowManager
import android.widget.Button
import android.widget.FrameLayout
import android.widget.ImageButton
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.ActivityResult
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.google.android.material.navigation.NavigationBarView
import com.hazelpay.merchant.tap2pay.utils.ConnectivityChecker
import com.hazelpay.merchant.tap2pay.utils.ResponseCodes
import com.hazelpay.merchant.tap2pay.utils.SdkUtils
import com.hazelpay.merchant.tap2pay.utils.createQRPayment
import com.hazelpay.merchant.tap2pay.utils.enforceOrientation
import com.hazelpay.merchant.tap2pay.utils.fetchAuthToken
import com.hazelpay.merchant.tap2pay.utils.fetchConnectionToken
import com.hazelpay.merchant.tap2pay.utils.fetchPaymentToken
import com.hazelpay.merchant.tap2pay.utils.getActiveCredentials
import com.hazelpay.merchant.tap2pay.utils.getFromSharedPreferences
import com.hazelpay.merchant.tap2pay.utils.getUserParentData
import com.hazelpay.merchant.tap2pay.utils.getUserTerminalData
import com.hazelpay.merchant.tap2pay.utils.saveToSharedPreferences
import com.ibagroup.tapxphone.sdk.SdkReturnStruct
import com.ibagroup.tapxphone.sdk.TxpSdkLauncherEventListener
import kotlinx.coroutines.launch
import java.text.DecimalFormat

class ConfirmationScreen : AppCompatActivity(), TxpSdkLauncherEventListener {
    private var selectedTipPercentage: Double = 0.0
    private var isReturningFromPayment = false
    private var paymentMethodSelected = "0"
    private var nfc: NfcAdapter? = null
    private var nfcTag: Tag? = null

    private lateinit var intentLauncher: ActivityResultLauncher<Intent>
    private var baseAmount: Double = 0.0
    private var totalAmount: String = ""
    private var tipAmount: Double = 0.0
    private lateinit var referenceId: String
    private lateinit var currency: String

    private val MERCHANT_BANK_ID: String = BuildConfig.MERCHANT_BANK_ID
    private val SOLUTION_PARTNER_ID: String = BuildConfig.SOLUTION_PARTNER_ID
    private val APP_LANGUAGE_CODE: String = "en"
    private var PAYMENT_TOKEN: String = ""
    private var INTENT_RESULT: String = ""
    private lateinit var closeButton: Button
    private lateinit var connectivityChecker: ConnectivityChecker

    override fun onResume() {
        super.onResume()

        if (isReturningFromPayment) {
            hideSDKStatus()
            if (INTENT_RESULT == "INTENT-0") {
                finish()
                startActivity(Intent(this, TransferSuccessfulScreen::class.java))
            } else {
                finish()
                startActivity(Intent(this, TransferFailedScreen::class.java))
            }
            isReturningFromPayment = false
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_confirmation_screen)

        enforceOrientation(this)
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);

        setupBottomNavigation()
        baseAmount = intent.getStringExtra("amount")?.toDoubleOrNull() ?: 0.0
        referenceId = intent.getStringExtra("referenceId") ?: ""
        paymentMethodSelected = intent.getStringExtra("selectedPaymentMethod") ?: "0"
        currency = getUserParentData()?.currency!!

        val tip5Button = findViewById<ImageButton>(R.id.tip_5)
        val tip10Button = findViewById<ImageButton>(R.id.tip_10)
        val tip15Button = findViewById<ImageButton>(R.id.tip_15)
        val tip20Button = findViewById<ImageButton>(R.id.tip_20)
        val tipNoneButton = findViewById<ImageButton>(R.id.tip_none)
        val confirmButton = findViewById<ImageButton>(R.id.confirm_button)
        val cancelButton = findViewById<ImageButton>(R.id.cancel_button)
        val tipOptionsContainer = findViewById<View>(R.id.tip_options_container)
        val selectTipText = findViewById<TextView>(R.id.select_tip_text)
        val divider = findViewById<View>(R.id.divider)
        var isTipSelected = false

        closeButton = findViewById(R.id.sdk_status_close_button)
        connectivityChecker = ConnectivityChecker(this)

        closeButton.setOnClickListener {
            val sdkStatusOverlay = findViewById<FrameLayout>(R.id.sdk_status_overlay)
            sdkStatusOverlay.visibility = View.GONE
        }

        val isTipEnabled = getFromSharedPreferences("TIP")?.toBoolean() == true

        if(paymentMethodSelected == "2" || !isTipEnabled){
            tipOptionsContainer.visibility = View.GONE
            selectTipText.visibility = View.GONE
            divider.visibility = View.GONE
        }

        updateAmountDisplay(selectedTipPercentage)

        tip5Button.setOnClickListener {
            selectedTipPercentage = 5.0
            isTipSelected = true
            updateButtonSelection(tip5Button, tip10Button, tip15Button, tip20Button, tipNoneButton)
            updateAmountDisplay(selectedTipPercentage)
        }
        tip10Button.setOnClickListener {
            selectedTipPercentage = 10.0
            isTipSelected = true
            updateButtonSelection(tip10Button, tip5Button, tip15Button, tip20Button, tipNoneButton)
            updateAmountDisplay(selectedTipPercentage)
        }
        tip15Button.setOnClickListener {
            selectedTipPercentage = 15.0
            isTipSelected = true
            updateButtonSelection(tip15Button, tip5Button, tip10Button, tip20Button, tipNoneButton)
            updateAmountDisplay(selectedTipPercentage)
        }
        tip20Button.setOnClickListener {
            selectedTipPercentage = 20.0
            isTipSelected = true
            updateButtonSelection(tip20Button, tip5Button, tip10Button, tip15Button, tipNoneButton)
            updateAmountDisplay(selectedTipPercentage)
        }
        tipNoneButton.setOnClickListener {
            selectedTipPercentage = 0.0
            isTipSelected = true
            updateButtonSelection(tipNoneButton, tip5Button, tip10Button, tip15Button, tip20Button)
            updateAmountDisplay(selectedTipPercentage)
        }

        try {
            intentLauncher = registerForActivityResult(
                ActivityResultContracts.StartActivityForResult()
            ) { result: ActivityResult ->
                val res = result.data
                if (res != null) {
                    INTENT_RESULT = res.getStringExtra("intent_result").toString()
                    val trnReceipt = res.getStringExtra("trn_receipt")
                    if (!trnReceipt.isNullOrEmpty()) {
                        saveToSharedPreferences("trn_receipt", trnReceipt)
                        val formattedTipPercentage = "${selectedTipPercentage.toInt()}%"
                        saveToSharedPreferences("tip_percentage", formattedTipPercentage)
                        val decimalFormat = DecimalFormat("0.00")
                        val formattedTipAmount = decimalFormat.format(tipAmount)
                        saveToSharedPreferences("tip_amount", formattedTipAmount)
                    } else {
                        println("trn_receipt is missing or empty in the Intent.")
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

        nfc = NfcAdapter.getDefaultAdapter(this)

        confirmButton.setOnClickListener {
            confirmButton.isEnabled = false

            if (paymentMethodSelected == "1" && isTipEnabled && !isTipSelected) {
                Toast.makeText(
                    this,
                    "Please select a tip percentage",
                    Toast.LENGTH_LONG
                ).show()
                confirmButton.isEnabled = true
                return@setOnClickListener
            }

            tipAmount = baseAmount * (selectedTipPercentage / 100.0)
            val totalAmountDouble = baseAmount + tipAmount
            val decimalFormat = DecimalFormat("0.00")
            totalAmount = decimalFormat.format(totalAmountDouble)

            // Check if we need to add isStaticQrMode to the intent
            val isStaticQrMode = intent.getBooleanExtra("isStaticQrMode", false)

            when (paymentMethodSelected) {
                "1" -> { // Card Payment
                    if (nfc != null) {
                        if (!nfc!!.isEnabled) {
                            Toast.makeText(
                                this,
                                "NFC is disabled. Please enable NFC to proceed.",
                                Toast.LENGTH_LONG
                            ).show()
                            val intent = Intent(Settings.ACTION_NFC_SETTINGS)
                            startActivity(intent)
                            confirmButton.isEnabled = true
                            return@setOnClickListener
                        }

                        nfc?.enableReaderMode(
                            this,
                            { tag: Tag? ->
                                if (tag != null) {
                                    nfcTag = tag
                                }
                            },
                            NfcAdapter.FLAG_READER_NFC_A
                                    or NfcAdapter.FLAG_READER_NFC_B
                                    or NfcAdapter.FLAG_READER_NO_PLATFORM_SOUNDS
                                    or NfcAdapter.FLAG_READER_SKIP_NDEF_CHECK,
                            null
                        )
                    } else {
                        confirmButton.isEnabled = true
                        Toast.makeText(
                            this,
                            "NFC is not supported on this device.",
                            Toast.LENGTH_SHORT
                        ).show()
                        return@setOnClickListener
                    }

                    lifecycleScope.launch {
                        updateUIWithMessage("Processing your payment")
                        showSDKStatus()
                        paymentSDK()
                        confirmButton.isEnabled = true
                    }
                }
                "2" -> {
                    lifecycleScope.launch {
                        ecommerceFlow()
                        confirmButton.isEnabled = true
                    }
                }
                else -> {
                    Toast.makeText(
                        this,
                        "Invalid payment method selected",
                        Toast.LENGTH_SHORT
                    ).show()
                    confirmButton.isEnabled = true
                }
            }
        }

        cancelButton.setOnClickListener {
            finish()
        }
    }

    private fun setupBottomNavigation() {
        val bottomNavigationView: BottomNavigationView = findViewById(R.id.bottom_navigation)
        bottomNavigationView.labelVisibilityMode = NavigationBarView.LABEL_VISIBILITY_SELECTED
        bottomNavigationView.selectedItemId = R.id.sales

        bottomNavigationView.setOnItemSelectedListener { item ->
            when (item.itemId) {
                R.id.home -> {
                    startActivity(Intent(this, HomeScreen::class.java))
                    finish()
                    true
                }
                R.id.sales -> {
                    true
                }
                R.id.reports -> {
                    startActivity(Intent(this, ReportsScreen::class.java))
                    finish()
                    true
                }
                R.id.settings -> {
                    startActivity(Intent(this, SettingsScreen::class.java))
                    finish()
                    true
                }
                else -> false
            }
        }

        val isOpen = getFromSharedPreferences("Terminal_access").toBoolean()
        bottomNavigationView.menu.findItem(R.id.sales).isEnabled = isOpen
    }

    private fun updateButtonSelection(selected: ImageButton, vararg others: ImageButton) {
        val buttonToTextViewMap = mapOf(
            R.id.tip_5 to R.id.tip_5_text,
            R.id.tip_10 to R.id.tip_10_text,
            R.id.tip_15 to R.id.tip_15_text,
            R.id.tip_20 to R.id.tip_20_text,
            R.id.tip_none to R.id.tip_none_text
        )

        selected.background = ContextCompat.getDrawable(this, R.drawable.selected_button)
        val selectedTextViewId = buttonToTextViewMap[selected.id]
        selectedTextViewId?.let {
            findViewById<TextView>(it).setTextColor(ContextCompat.getColor(this, android.R.color.white))
        }

        others.forEach { button ->
            button.background = ContextCompat.getDrawable(this, R.drawable.button_outline_green)
            val textViewId = buttonToTextViewMap[button.id]
            textViewId?.let {
                findViewById<TextView>(it).setTextColor(ContextCompat.getColor(this, android.R.color.black))
            }
        }
    }

    private fun updateAmountDisplay(tipPercentage: Double) {
        val decimalFormat = DecimalFormat("0.00")
        tipAmount = baseAmount * (tipPercentage / 100.0)
        val formattedAmount = decimalFormat.format(baseAmount)
        findViewById<TextView>(R.id.total_amount).text = "$currency $formattedAmount"

        val tipAmountText = findViewById<TextView>(R.id.tip_amount)
        if (tipPercentage > 0) {
            val formattedTip = decimalFormat.format(tipAmount)
            tipAmountText.text = "+ $currency $formattedTip Tip"
            tipAmountText.visibility = View.VISIBLE
        }else{
            tipAmountText.visibility = View.GONE
        }
    }

    private fun ecommerceFlow() {
        updateUIWithMessage("Generating QR code")
        showSDKStatus()
        lifecycleScope.launch {
            val credential = getActiveCredentials(this@ConfirmationScreen)

            if (credential == null) {
                hideSDKStatus()
                Toast.makeText(this@ConfirmationScreen, "Failed to fetch credentials", Toast.LENGTH_SHORT).show()
                return@launch
            }

            val paymentId = createQRPayment(
                context = this@ConfirmationScreen,
                amount = totalAmount,
                referenceId = referenceId,
                currency = currency,
            )

            hideSDKStatus()

            if (paymentId != null) {
                if (selectedTipPercentage > 0) {
                    val formattedTipPercentage = "${selectedTipPercentage.toInt()}%"
                    saveToSharedPreferences("tip_percentage", formattedTipPercentage)
                    saveToSharedPreferences("tip_amount", tipAmount.toString())
                }

                val intent = Intent(this@ConfirmationScreen, QrCodeScreen::class.java).apply {
                    putExtra("PAYMENT_ID", paymentId)
                }
                startActivity(intent)
                finish() // Close the confirmation screen
            } else {
                Toast.makeText(this@ConfirmationScreen, "Failed to create payment", Toast.LENGTH_SHORT).show()
                hideSDKStatus()
            }
        }
    }

    private fun fetchTokenForPayment(
        amount: String,
        referenceId: String,
        currency: String,
        callback: (String?) -> Unit
    ) {
        lifecycleScope.launch {
            val token = fetchPaymentToken(amount, referenceId, getUserParentData()?.email!!, currency)
            if (token == null) {
                Log.e("TOKENS", "Failed to fetch payment token!")
                callback(null)
                return@launch
            }
            callback(token)
        }
    }

    private fun paymentSDK() {
        fetchTokenForPayment(totalAmount, referenceId, currency) { token ->
            if (token != null) {
                Log.d("PaymentToken", "Payment Token: $token")
                PAYMENT_TOKEN = token
                SdkUtils.paymentSDKMethod(
                    context = this,
                    activityContext = this,
                    nfcTag = nfcTag,
                    paymentToken = PAYMENT_TOKEN,
                    merchantBankId = MERCHANT_BANK_ID,
                    appLanguageCode = APP_LANGUAGE_CODE,
                    solutionPartnerId = SOLUTION_PARTNER_ID,
                    activityLauncher = intentLauncher,
                ) { isReturningFromPaymentScreen, sdkResult ->
                    isReturningFromPayment = isReturningFromPaymentScreen
                    handlePaymentSDKResponse(sdkResult)
                }
            } else {
                hideSDKStatus()
                Toast.makeText(
                    this,
                    "Failed to gather secure payment information",
                    Toast.LENGTH_LONG
                ).show()
                findViewById<ImageButton>(R.id.confirm_button).isEnabled = true
            }
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        enforceOrientation(this)
    }

    override fun onCheckRequest(num: Int): String {
        return when (num) {
            21 -> "Integrity check result"
            33 -> "Side load check result"
            else -> ""
        }
    }

    private fun handleSDKRetryResponse(retrySuccess: SdkReturnStruct?) {
        val responseMessage = ResponseCodes.responseMessages[retrySuccess?.responseCode]

        runOnUiThread {
            when (retrySuccess?.responseCode) {
                "0" -> {
                    paymentSDK()
                }
                "910" -> {
                    startTerminalReLinkProcess(this@ConfirmationScreen)
                }
                else -> {
                    updateUIWithMessage(
                        "Payment Initialization failed: ${responseMessage ?: "Unknown error, Code: ${retrySuccess?.description}"}"
                    )
                    runOnUiThread {
                        closeButton.visibility = View.VISIBLE
                        findViewById<ProgressBar>(R.id.sdk_status_progress).visibility = View.GONE
                    }
                }
            }
        }
    }

    private fun handlePaymentSDKResponse(paymentRes: SdkReturnStruct?) {
        val responseMessage = ResponseCodes.responseMessages[paymentRes?.responseCode]

        runOnUiThread {
            when (paymentRes?.responseCode) {
                "0" -> {
                    hideSDKStatus()
                }
                "910" -> {
                    startTerminalReLinkProcess(this@ConfirmationScreen)
                }
                else -> {
                    updateUIWithMessage(
                        "Payment Initialization failed: ${responseMessage ?: "Unknown error, Code: ${paymentRes?.description}"}"
                    )
                    runOnUiThread {
                        closeButton.visibility = View.VISIBLE
                        findViewById<ProgressBar>(R.id.sdk_status_progress).visibility = View.GONE
                    }
                }
            }
        }
    }

    private fun startTerminalReLinkProcess(context: Context) {
        updateUIWithMessage("Relinking Device to Payment Terminal")
        showSDKStatus()

        lifecycleScope.launch {
            val authToken = fetchAuthToken(context) ?: return@launch
            val terminalId = getUserTerminalData()?.terminalName ?: return@launch
            val (success, connectionToken) = fetchConnectionToken(
                this@ConfirmationScreen,
                authToken,
                terminalId,
                getUserParentData()?.email!!,
                "RELINK"
            )

            if (!success || connectionToken == null) {
                hideSDKStatus()
                return@launch
            }

            SdkUtils.deviceSetupSDKMethod(
                merchantBankId = MERCHANT_BANK_ID,
                appLanguageCode = APP_LANGUAGE_CODE,
                solutionPartnerId = SOLUTION_PARTNER_ID,
                connectionToken = connectionToken,
                context = this@ConfirmationScreen
            ) { setupResponse ->
                val setupResponseMessage =
                    ResponseCodes.responseMessages[setupResponse?.responseCode]
                runOnUiThread {
                    if (setupResponse?.responseCode == "909") {
                        updateUIWithMessage("Relinking Complete")

                        SdkUtils.startSDKMethod(
                            merchantBankId = MERCHANT_BANK_ID,
                            appLanguageCode = APP_LANGUAGE_CODE,
                            solutionPartnerId = SOLUTION_PARTNER_ID,
                            context = this@ConfirmationScreen,
                            activityContext = this@ConfirmationScreen
                        ) { retrySuccess ->
                            handleSDKRetryResponse(retrySuccess)
                        }

                    } else {
                        updateUIWithMessage(
                            "Device Configuration failed: ${
                                setupResponseMessage ?: "Unknown error, Code: ${setupResponse?.description}"
                            }"
                        )
                        closeButton.visibility = View.VISIBLE
                        findViewById<ProgressBar>(R.id.sdk_status_progress).visibility = View.GONE
                    }
                }
            }
        }
    }

    private fun updateUIWithMessage(message: String) {
        runOnUiThread {
            findViewById<ProgressBar>(R.id.sdk_status_progress).visibility = View.VISIBLE
            findViewById<TextView>(R.id.sdk_status_message).text = message
        }
    }

    private fun showSDKStatus() {
        runOnUiThread {
            findViewById<View>(R.id.sdk_status_overlay).visibility = View.VISIBLE
            findViewById<ProgressBar>(R.id.sdk_status_progress).visibility = View.VISIBLE
            closeButton.visibility = View.GONE
        }
    }

    private fun hideSDKStatus() {
        runOnUiThread {
            findViewById<ProgressBar>(R.id.sdk_status_progress).visibility = View.GONE
            findViewById<View>(R.id.sdk_status_overlay).visibility = View.GONE
            closeButton.visibility = View.GONE
        }
    }

    override fun onStart() {
        super.onStart()
        connectivityChecker.startListening()
    }

    override fun onStop() {
        super.onStop()
        connectivityChecker.stopListening()
        nfc?.disableReaderMode(this)
    }
}