package com.hazelpay.merchant.tap2pay

import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.text.InputType
import android.view.WindowManager
import android.widget.EditText
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.hazelpay.merchant.tap2pay.utils.enforceOrientation

class ChangePasswordScreen : AppCompatActivity() {

    private lateinit var oldPasswordField: EditText
    private lateinit var newPasswordField: EditText
    private lateinit var confirmPasswordField: EditText

    private lateinit var newPasswordToggle: ImageView
    private lateinit var confirmPasswordToggle: ImageView

    private var isNewPasswordVisible = false
    private var isConfirmPasswordVisible = false

    override fun onCreate(savedInstanceState: Bundle?) {
        setContentView(R.layout.activity_change_password_screen)
        super.onCreate(savedInstanceState)
        enforceOrientation(this)

        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

        oldPasswordField = findViewById(R.id.old_pass)
        newPasswordField = findViewById(R.id.password)
        confirmPasswordField = findViewById(R.id.confirm_password)

        newPasswordToggle = findViewById(R.id.password_toggle)
        confirmPasswordToggle = findViewById(R.id.confirm_password_toggle)

        newPasswordToggle.setOnClickListener {
            isNewPasswordVisible = !isNewPasswordVisible
            togglePasswordVisibility(newPasswordField, isNewPasswordVisible)
        }

        confirmPasswordToggle.setOnClickListener {
            isConfirmPasswordVisible = !isConfirmPasswordVisible
            togglePasswordVisibility(confirmPasswordField, isConfirmPasswordVisible)
        }

        val saveButton: ImageButton = findViewById(R.id.save_pass_button)
        saveButton.setOnClickListener { savePassword() }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        enforceOrientation(this)
    }

    private fun togglePasswordVisibility(passwordField: EditText, isVisible: Boolean) {
        if (isVisible) {
            passwordField.inputType = InputType.TYPE_CLASS_TEXT or InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD
        } else {
            passwordField.inputType = InputType.TYPE_CLASS_TEXT or InputType.TYPE_TEXT_VARIATION_PASSWORD
        }
        passwordField.setSelection(passwordField.text.length)  // Keep the cursor at the end
    }

    private fun savePassword() {
        val oldPassword = oldPasswordField.text.toString()
        val newPassword = newPasswordField.text.toString()
        val confirmPassword = confirmPasswordField.text.toString()

        if (oldPassword.isEmpty()) {
            Toast.makeText(this, getString(R.string.enter_old_password), Toast.LENGTH_SHORT).show()
            return
        }

        if (!isValidPassword(newPassword)) {
            Toast.makeText(this, getString(R.string.invalid_password_message), Toast.LENGTH_LONG).show()
            return
        }

        if (newPassword != confirmPassword) {
            Toast.makeText(this, getString(R.string.passwords_do_not_match), Toast.LENGTH_SHORT).show()
            return
        }

        Toast.makeText(this, getString(R.string.password_changed_success), Toast.LENGTH_SHORT).show()
        val intent = Intent(this, HomeScreen::class.java)
        startActivity(intent)
        finish()
    }

    private fun isValidPassword(password: String): Boolean {

        val passwordPattern = Regex("^(?=.*[A-Z])(?=.*[a-z])(?=.*\\d)(?=.*[^A-Za-z\\d]).{8,}$")
        return passwordPattern.matches(password)
    }
}
