import java.util.Properties

plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.jetbrains.kotlin.android)
}

val props = Properties().apply {
    rootProject.file("gradle.properties").inputStream().use { load(it) }
}

android {
    namespace = "com.hazelpay.merchant.tap2pay"
    //noinspection GradleDependency
    compileSdk = 34

    defaultConfig {
        applicationId = "com.hazelsone.merchant"
        minSdk = 26
        //noinspection OldTargetApi
        targetSdk = 34
        versionCode = 2
        versionName = "1.0"
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro"
            )
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = "1.8"
    }
    buildFeatures {
        viewBinding = true
        buildConfig = true

    }

    flavorDimensions += "environment"
    productFlavors {
        create("preprod") {
            dimension = "environment"
            buildConfigField("String", "BASE_URL", "\"${props.getProperty("preprodBaseUrl")}\"")
            buildConfigField("String", "MERCHANT_BANK_ID", "\"${props.getProperty("preprodMerchantBankId")}\"")
            buildConfigField("String", "SOLUTION_PARTNER_ID", "\"${props.getProperty("preprodSolutionPartnerId")}\"")
            buildConfigField("String", "AUTH_USERNAME", "\"${props.getProperty("preprodAuthUsername")}\"")
            buildConfigField("String", "AUTH_PASSWORD", "\"${props.getProperty("preprodAuthPassword")}\"")
        }

        create("prod") {
            dimension = "environment"
            buildConfigField("String", "BASE_URL", "\"${props.getProperty("prodBaseUrl")}\"")
            buildConfigField("String", "MERCHANT_BANK_ID", "\"${props.getProperty("prodMerchantBankId")}\"")
            buildConfigField("String", "SOLUTION_PARTNER_ID", "\"${props.getProperty("prodSolutionPartnerId")}\"")
            buildConfigField("String", "AUTH_USERNAME", "\"${props.getProperty("prodAuthUsername")}\"")
            buildConfigField("String", "AUTH_PASSWORD", "\"${props.getProperty("prodAuthPassword")}\"")
        }
    }
}

dependencies {
    implementation(fileTree(mapOf("dir" to "lib", "include" to listOf("*.jar"))))
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.appcompat)
    implementation(libs.material)
    implementation(libs.androidx.constraintlayout)
    implementation(libs.androidx.lifecycle.livedata.ktx)
    implementation(libs.androidx.lifecycle.viewmodel.ktx)
    implementation(libs.androidx.navigation.fragment.ktx)
    implementation(libs.androidx.navigation.ui.ktx)
    implementation(libs.androidx.activity)
    implementation(libs.androidx.fragment.ktx)
    implementation(libs.junit.junit)
    implementation(libs.androidx.junit.ktx)
    implementation(libs.support.annotations)
    testImplementation(libs.junit)
    testImplementation(libs.mockitoCore)
    testImplementation(libs.mockitoKotlin)
    testImplementation(libs.mockitoKotlin)
    testImplementation(libs.mockitoCore)
    androidTestImplementation(libs.androidx.runner)
    androidTestImplementation(libs.androidx.espresso.core.v350)
    androidTestImplementation(libs.androidx.junit.v121)
    androidTestImplementation(libs.androidx.espresso.core.v361)
    implementation(libs.retrofit)
    implementation(libs.okhttp)
    implementation(libs.retrofit.gson)
    implementation(libs.okhttp.logging)
    implementation(files("../lib/txp_sdk.aar"))
    implementation(libs.signature.pad)
    implementation(libs.androidx.cardview)
    implementation(libs.androidx.startup.runtime)
    implementation(libs.androidx.profileinstaller)
    implementation(libs.android.gif.drawable)
    implementation(libs.gson.v2101)
    implementation(libs.play.services.safetynet)
    implementation(libs.play.services.code.scanner)
    implementation(libs.play.services.vision)
    implementation(libs.integrity)
    implementation(libs.core)
    implementation(libs.commons.io)
    implementation(libs.okhttp.v4120)
    implementation(platform(libs.firebase.bom))
    implementation(libs.firebase.appcheck.playintegrity)
    implementation(libs.androidx.biometric)
    implementation(libs.nimbus.jose.jwt)
    implementation (libs.androidx.biometric.v120alpha05)
    implementation (libs.app.update)
    implementation (libs.app.update.ktx)
    implementation(libs.kotlinx.serialization.json)
    implementation(libs.retrofit2.kotlinx.serialization.converter)
    implementation (libs.androidx.swiperefreshlayout)
}
